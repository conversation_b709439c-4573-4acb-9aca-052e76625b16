#!/usr/bin/env python3
"""
Process 1-minute historical data to generate 3min, 5min, and 15min timeframes
Uses Polars and PyArrow for high-performance processing
"""

import os
import polars as pl
import pyarrow as pa
from pathlib import Path
from datetime import datetime
import logging

# Set environment variable to ignore timezone parsing errors
os.environ['POLARS_IGNORE_TIMEZONE_PARSE_ERROR'] = '1'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TimeframeProcessor:
    def __init__(self, input_file: str, output_base_dir: str = "Option/data/historical"):
        """
        Initialize the timeframe processor
        
        Args:
            input_file: Path to the 1-minute parquet file
            output_base_dir: Base directory for output files
        """
        self.input_file = input_file
        self.output_base_dir = Path(output_base_dir)
        
        # Define timeframe configurations
        self.timeframes = {
            "3min": 3,
            "5min": 5,
            "15min": 15
        }
        
        # Create output directories
        for tf in self.timeframes.keys():
            (self.output_base_dir / tf).mkdir(parents=True, exist_ok=True)
    
    def load_and_prepare_data(self) -> pl.DataFrame:
        """Load 1-minute data and prepare datetime column"""
        logger.info(f"Loading data from {self.input_file}")
        
        # Read the parquet file
        df = pl.read_parquet(self.input_file)
        logger.info(f"Loaded {df.height:,} rows with {df.width} columns")
        
        # Create proper datetime column from date and time
        df = df.with_columns([
            pl.concat_str([
                pl.col("date").cast(pl.Utf8),
                pl.lit(" "),
                pl.col("time").cast(pl.Utf8)
            ]).str.strptime(pl.Datetime, "%Y-%m-%d %H:%M:%S").alias("datetime")
        ])
        
        logger.info("Created datetime column from date and time")
        return df
    
    def resample_to_timeframe(self, df: pl.DataFrame, minutes: int) -> pl.DataFrame:
        """
        Resample 1-minute data to specified timeframe
        
        Args:
            df: Input dataframe with 1-minute data
            minutes: Target timeframe in minutes (3, 5, or 15)
        
        Returns:
            Resampled dataframe
        """
        logger.info(f"Resampling to {minutes}-minute timeframe")
        
        # For options data, we need to group by symbol and resample
        resampled = (
            df
            .with_columns([
                pl.col("datetime").dt.truncate(f"{minutes}m").alias("timeframe_start")
            ])
            .group_by(["symbol", "option_type", "strike_price", "timeframe_start"])
            .agg([
                # OHLCV aggregation
                pl.col("open").first().alias("open"),
                pl.col("high").max().alias("high"),
                pl.col("low").min().alias("low"),
                pl.col("close").last().alias("close"),
                pl.col("volume").sum().alias("volume"),
                # Keep other fields from first record
                pl.col("index").first().alias("index"),
                pl.col("token").first().alias("token"),
                pl.col("date").first().alias("date"),
                pl.col("time").first().alias("time")
            ])
            .rename({"timeframe_start": "datetime"})
            .sort(["symbol", "datetime"])
        )
        
        # Update date and time columns to reflect the resampled timeframe
        resampled = resampled.with_columns([
            pl.col("datetime").dt.date().alias("date"),
            pl.col("datetime").dt.time().alias("time"),
            pl.col("datetime").alias("timestamp")  # Keep datetime as timestamp too
        ])
        
        logger.info(f"Resampled to {resampled.height:,} rows")
        return resampled
    
    def save_timeframe_data(self, df: pl.DataFrame, timeframe: str):
        """Save resampled data to parquet file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"historical_{timeframe}_{timestamp}.parquet"
        filepath = self.output_base_dir / timeframe / filename
        
        # Save with compression
        df.write_parquet(filepath, compression="snappy")
        logger.info(f"Saved {df.height:,} rows to {filepath}")
    
    def process_all_timeframes(self):
        """Main method to process all timeframes"""
        logger.info("Starting timeframe processing")
        
        # Load and prepare data
        df = self.load_and_prepare_data()
        
        # Process each timeframe
        for timeframe, minutes in self.timeframes.items():
            logger.info(f"\n{'='*50}")
            logger.info(f"Processing {timeframe} timeframe")
            logger.info(f"{'='*50}")
            
            try:
                # Resample data
                resampled_df = self.resample_to_timeframe(df, minutes)
                
                # Save data
                self.save_timeframe_data(resampled_df, timeframe)
                
                logger.info(f"✅ Successfully processed {timeframe} timeframe")
                
            except Exception as e:
                logger.error(f"❌ Error processing {timeframe}: {e}")
                continue
        
        logger.info(f"\n🎉 Timeframe processing completed!")
        logger.info(f"📁 Output files saved to {self.output_base_dir}")

def main():
    """Main function"""
    input_file = "Option/data/historical/1min/historical_1min.parquet"
    
    if not Path(input_file).exists():
        logger.error(f"Input file not found: {input_file}")
        return
    
    # Create processor and run
    processor = TimeframeProcessor(input_file)
    processor.process_all_timeframes()

if __name__ == "__main__":
    main()
