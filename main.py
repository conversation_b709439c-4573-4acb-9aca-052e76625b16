#!/usr/bin/env python3
"""
[INIT] CENTRALIZED MAIN ENTRY POINT FOR NIFTY & BANK NIFTY OPTIONS TRADING SYSTEM
═══════════════════════════════════════════════════════════════════════════════

This is the unified entry point for all options trading system agents and workflows.
Supports both individual agent execution and complete workflow orchestration.

Features:
[TARGET] Individual agent execution with custom configurations
[WOR<PERSON>FLOW] Complete workflow orchestration with dependency management
[FAST] GPU optimization and performance monitoring for options
[STATUS] Real-time status monitoring and health checks
[SECURITY] Error handling and graceful shutdown
[METRICS] Performance metrics and logging
[OPTIONS] Specialized for NIFTY & BANK NIFTY options only
[TIMEFRAMES] Multi-timeframe support: 1min→3min,5min,15min data processing
"""

import asyncio
import argparse
import sys
import logging
import signal
import os
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# Set environment variable to handle timezone parsing issues globally
os.environ['POLARS_IGNORE_TIMEZONE_PARSE_ERROR'] = '1'

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import options agents
from agents.options_data_ingestion_agent import OptionsDataIngestionAgent
from agents.options_feature_engineering_agent import OptionsFeatureEngineeringAgent
from agents.options_strategy_generation_agent import OptionsStrategyGenerationAgent
from agents.options_backtesting_agent import OptionsBacktestingAgent
from agents.options_ai_training_agent import OptionsAITrainingAgent
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent
from agents.options_signal_generation_agent import OptionsSignalGenerationAgent
from agents.options_risk_management_agent import OptionsRiskManagementAgent
from agents.options_execution_agent import OptionsExecutionAgent
from agents.options_performance_analysis_agent import OptionsPerformanceAnalysisAgent
from agents.options_llm_interface_agent import OptionsLLMInterfaceAgent
from agents.options_strategy_evolution_agent import OptionsStrategyEvolutionAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/options_main.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptionsSystemOrchestrator:
    """
    Main orchestrator for the Options Trading System
    
    Manages all 12 agents and their workflows:
    1. Data Ingestion Agent
    2. Feature Engineering Agent  
    3. Strategy Generation Agent
    4. Backtesting Agent
    5. AI Training Agent
    6. Market Monitoring Agent
    7. Signal Generation Agent
    8. Risk Management Agent
    9. Execution Agent
    10. Performance Analysis Agent
    11. LLM Interface Agent
    12. Strategy Evolution Agent
    """
    
    def __init__(self):
        """Initialize the Options System Orchestrator"""
        self.agents = {}
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("[INIT] Options System Orchestrator initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"[SIGNAL] Received signal {signum}, initiating shutdown...")
        self.shutdown_event.set()
    
    async def run_agent(self, agent_name: str, config_path: Optional[str] = None, **kwargs) -> bool:
        """Run a specific options trading agent"""
        try:
            logger.info(f"[AGENT] Starting {agent_name} agent...")
            
            # Agent mapping with their classes
            agent_classes = {
                'data_ingestion': OptionsDataIngestionAgent,
                'feature_engineering': OptionsFeatureEngineeringAgent,
                'strategy_generation': OptionsStrategyGenerationAgent,
                'backtesting': OptionsBacktestingAgent,
                'ai_training': OptionsAITrainingAgent,
                'market_monitoring': OptionsMarketMonitoringAgent,
                'signal_generation': OptionsSignalGenerationAgent,
                'risk_management': OptionsRiskManagementAgent,
                'execution': OptionsExecutionAgent,
                'performance_analysis': OptionsPerformanceAnalysisAgent,
                'llm_interface': OptionsLLMInterfaceAgent,
                'strategy_evolution': OptionsStrategyEvolutionAgent
            }
            
            if agent_name not in agent_classes:
                logger.error(f"[ERROR] Unknown agent: {agent_name}")
                return False
            
            # Initialize agent
            agent_class = agent_classes[agent_name]
            if config_path:
                agent = agent_class(config_path=config_path)
            else:
                agent = agent_class()
            
            # Store agent reference
            self.agents[agent_name] = agent
            
            # Initialize and start agent
            await agent.initialize()
            success = await agent.start(**kwargs)
            
            if success:
                logger.info(f"[SUCCESS] {agent_name} agent started successfully")
            else:
                logger.error(f"[ERROR] {agent_name} agent failed to start")
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Error running {agent_name} agent: {e}")
            return False
    
    async def run_workflow(self, workflow_name: str, **kwargs) -> bool:
        """Run a complete options trading workflow"""
        try:
            logger.info(f"[WORKFLOW] Starting {workflow_name} workflow...")
            
            workflows = {
                'full_pipeline': self._workflow_full_pipeline,
                'training_pipeline': self._workflow_training_pipeline,
                'live_trading': self._workflow_live_trading,
                'data_pipeline': self._workflow_data_pipeline,
                'strategy_development': self._workflow_strategy_development,
                'options_research': self._workflow_options_research,
                'multi_timeframe_analysis': self._workflow_multi_timeframe_analysis
            }
            
            if workflow_name not in workflows:
                logger.error(f"[ERROR] Unknown workflow: {workflow_name}")
                return False
            
            success = await workflows[workflow_name](**kwargs)
            
            if success:
                logger.info(f"[SUCCESS] {workflow_name} workflow completed successfully")
            else:
                logger.error(f"[ERROR] {workflow_name} workflow failed")
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Error running {workflow_name} workflow: {e}")
            return False
    
    async def _workflow_full_pipeline(self, **kwargs) -> bool:
        """Complete end-to-end options trading pipeline"""
        try:
            logger.info("[WORKFLOW] Starting full options pipeline workflow...")

            # Step 1: Data Ingestion
            if not await self.run_agent('data_ingestion'):
                return False

            # Step 2: Feature Engineering
            if not await self.run_agent('feature_engineering'):
                return False

            # Step 3: Strategy Generation
            if not await self.run_agent('strategy_generation'):
                return False

            # Step 4: Backtesting
            if not await self.run_agent('backtesting'):
                return False

            # Step 5: AI Training
            if not await self.run_agent('ai_training'):
                return False

            # Step 6: Start live trading agents
            await asyncio.gather(
                self.run_agent('market_monitoring'),
                self.run_agent('signal_generation'),
                self.run_agent('risk_management'),
                self.run_agent('execution'),
                self.run_agent('performance_analysis')
            )

            return True

        except Exception as e:
            logger.error(f"[ERROR] Full pipeline workflow failed: {e}")
            return False
    
    async def _workflow_live_trading(self, **kwargs) -> bool:
        """Live options trading workflow"""
        try:
            logger.info("[WORKFLOW] Starting live options trading workflow...")

            # Start all live trading agents concurrently
            tasks = [
                self.run_agent('market_monitoring'),
                self.run_agent('signal_generation'),
                self.run_agent('risk_management'),
                self.run_agent('execution'),
                self.run_agent('performance_analysis')
            ]

            # Wait for all agents to start
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check if any agent failed
            for i, result in enumerate(results):
                if isinstance(result, Exception) or not result:
                    logger.error(f"[ERROR] Live trading agent {i} failed: {result}")
                    return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Live trading workflow failed: {e}")
            return False
    
    async def _workflow_training_pipeline(self, **kwargs) -> bool:
        """Options AI training pipeline"""
        try:
            logger.info("[WORKFLOW] Starting options training pipeline...")

            # Data preparation
            if not await self.run_agent('data_ingestion'):
                return False

            if not await self.run_agent('feature_engineering'):
                return False

            # Strategy development and backtesting
            if not await self.run_agent('strategy_generation'):
                return False

            if not await self.run_agent('backtesting'):
                return False

            # AI training and evolution
            if not await self.run_agent('ai_training'):
                return False

            if not await self.run_agent('strategy_evolution'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Training pipeline workflow failed: {e}")
            return False
    
    async def _workflow_data_pipeline(self, **kwargs) -> bool:
        """Options data processing pipeline"""
        try:
            logger.info("[WORKFLOW] Starting options data pipeline...")

            # Data ingestion and processing
            if not await self.run_agent('data_ingestion'):
                return False

            if not await self.run_agent('feature_engineering'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Data pipeline workflow failed: {e}")
            return False
    
    async def _workflow_strategy_development(self, **kwargs) -> bool:
        """Options strategy development workflow"""
        try:
            logger.info("[WORKFLOW] Starting options strategy development...")

            # Strategy generation and testing
            if not await self.run_agent('strategy_generation'):
                return False

            if not await self.run_agent('backtesting'):
                return False

            if not await self.run_agent('strategy_evolution'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Strategy development workflow failed: {e}")
            return False
    
    async def _workflow_options_research(self, **kwargs) -> bool:
        """Options research and analysis workflow"""
        try:
            logger.info("[WORKFLOW] Starting options research workflow...")

            # Research and analysis
            if not await self.run_agent('data_ingestion'):
                return False

            if not await self.run_agent('feature_engineering'):
                return False

            if not await self.run_agent('performance_analysis'):
                return False

            if not await self.run_agent('llm_interface'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Options research workflow failed: {e}")
            return False

    async def _workflow_multi_timeframe_analysis(self, **kwargs) -> bool:
        """Multi-timeframe options analysis workflow"""
        try:
            logger.info("[WORKFLOW] Starting multi-timeframe options analysis...")

            # Step 1: Download 1-minute data and generate multi-timeframes
            logger.info("[STEP 1] Data ingestion with multi-timeframe generation...")
            if not await self.run_agent('data_ingestion'):
                logger.error("[ERROR] Data ingestion failed")
                return False

            # Step 2: Feature engineering for all timeframes
            logger.info("[STEP 2] Multi-timeframe feature engineering...")
            if not await self.run_agent('feature_engineering'):
                logger.error("[ERROR] Feature engineering failed")
                return False

            # Step 3: Start multi-timeframe market monitoring
            logger.info("[STEP 3] Starting multi-timeframe market monitoring...")
            monitoring_task = asyncio.create_task(self.run_agent('market_monitoring'))

            # Step 4: Generate strategies based on multi-timeframe analysis
            logger.info("[STEP 4] Multi-timeframe strategy generation...")
            if not await self.run_agent('strategy_generation'):
                logger.error("[ERROR] Strategy generation failed")
                return False

            # Step 5: Backtest strategies across timeframes
            logger.info("[STEP 5] Multi-timeframe backtesting...")
            if not await self.run_agent('backtesting'):
                logger.error("[ERROR] Backtesting failed")
                return False

            # Step 6: Train AI models on multi-timeframe data
            logger.info("[STEP 6] AI training on multi-timeframe features...")
            if not await self.run_agent('ai_training'):
                logger.error("[ERROR] AI training failed")
                return False

            # Step 7: Generate multi-timeframe signals
            logger.info("[STEP 7] Multi-timeframe signal generation...")
            signal_task = asyncio.create_task(self.run_agent('signal_generation'))

            # Step 8: Performance analysis across timeframes
            logger.info("[STEP 8] Multi-timeframe performance analysis...")
            if not await self.run_agent('performance_analysis'):
                logger.error("[ERROR] Performance analysis failed")
                return False

            # Wait for monitoring and signal generation to complete
            await asyncio.gather(monitoring_task, signal_task)

            logger.info("[SUCCESS] Multi-timeframe analysis workflow completed")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Multi-timeframe analysis workflow failed: {e}")
            return False

    async def cleanup(self):
        """Cleanup all agents and resources"""
        try:
            logger.info("[CLEANUP] Shutting down all agents...")
            
            for agent_name, agent in self.agents.items():
                try:
                    if hasattr(agent, 'cleanup'):
                        await agent.cleanup()
                    logger.info(f"[CLEANUP] {agent_name} agent cleaned up")
                except Exception as e:
                    logger.error(f"[ERROR] Error cleaning up {agent_name}: {e}")
            
            self.agents.clear()
            logger.info("[CLEANUP] All agents cleaned up successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Error during cleanup: {e}")

async def main():
    """Main entry point for the options trading system"""
    parser = argparse.ArgumentParser(
        description='[INIT] Nifty & Bank Nifty Options Trading System - Centralized Control Center',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
[TARGET] INDIVIDUAL AGENT EXAMPLES:
  python main.py --agent data_ingestion
  python main.py --agent feature_engineering
  python main.py --agent ai_training --config custom_config.yaml
  python main.py --agent backtesting --demo
  python main.py --agent signal_generation
  python main.py --agent market_monitoring
  python main.py --agent risk_management
  python main.py --agent execution --demo
  python main.py --agent performance_analysis
  python main.py --agent llm_interface
  python main.py --agent strategy_evolution

[WORKFLOW] COMPLETE WORKFLOW EXAMPLES:
  python main.py --workflow full_pipeline
  python main.py --workflow live_trading
  python main.py --workflow training_pipeline
  python main.py --workflow data_pipeline
  python main.py --workflow strategy_development
  python main.py --workflow options_research

[STATUS] MONITORING:
  python main.py --agent market_monitoring --config config/options_market_monitoring_config.yaml
  python main.py --workflow live_trading --monitor
        """
    )

    # Agent execution
    parser.add_argument(
        '--agent',
        choices=[
            'data_ingestion', 'feature_engineering', 'strategy_generation',
            'backtesting', 'ai_training', 'market_monitoring', 'signal_generation',
            'risk_management', 'execution', 'performance_analysis',
            'llm_interface', 'strategy_evolution'
        ],
        help='Run a specific options trading agent'
    )

    # Workflow execution
    parser.add_argument(
        '--workflow',
        choices=[
            'full_pipeline', 'training_pipeline', 'live_trading',
            'data_pipeline', 'strategy_development', 'options_research',
            'multi_timeframe_analysis'
        ],
        help='Run a complete options trading workflow'
    )

    # Configuration
    parser.add_argument(
        '--config',
        type=str,
        help='Path to configuration file'
    )

    # Demo mode
    parser.add_argument(
        '--demo',
        action='store_true',
        help='Run in demo mode'
    )

    # Monitoring
    parser.add_argument(
        '--monitor',
        action='store_true',
        help='Enable monitoring mode'
    )

    args = parser.parse_args()

    # Create orchestrator
    orchestrator = OptionsSystemOrchestrator()

    try:
        # Agent execution
        if args.agent:
            logger.info(f"[AGENT] Running {args.agent} agent...")
            success = await orchestrator.run_agent(
                args.agent,
                config_path=args.config,
                demo=args.demo
            )

            if success:
                print(f"[SUCCESS] {args.agent} agent completed successfully")
            else:
                print(f"[ERROR] {args.agent} agent failed")
                sys.exit(1)

            return

        # Workflow execution
        if args.workflow:
            logger.info(f"[WORKFLOW] Running {args.workflow} workflow...")
            success = await orchestrator.run_workflow(
                args.workflow,
                monitor=args.monitor
            )

            if success:
                print(f"[SUCCESS] {args.workflow} workflow completed successfully")
            else:
                print(f"[ERROR] {args.workflow} workflow failed")
                sys.exit(1)

            return

        # If no specific command, show help
        parser.print_help()

    except KeyboardInterrupt:
        logger.info("[EXIT] Options trading system interrupted by user")
    except Exception as e:
        logger.error(f"[ERROR] Fatal error: {e}")
        sys.exit(1)
    finally:
        await orchestrator.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
