#!/usr/bin/env python3
"""
Test script to verify websocket initialization fix
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.append(os.getcwd())

try:
    # Test imports
    print("Testing imports...")
    from SmartApi import SmartConnect, SmartWebSocket
    print("✅ SmartWebSocket import successful")
    
    try:
        from SmartApi.smartWebSocketV2 import SmartWebSocketV2
        print("✅ SmartWebSocketV2 import successful")
    except ImportError as e:
        print(f"❌ SmartWebSocketV2 import failed: {e}")
    
    # Test SmartWebSocket constructor signature
    import inspect
    sig = inspect.signature(SmartWebSocket.__init__)
    print(f"✅ SmartWebSocket constructor signature: {sig}")
    
    # Test with dummy parameters
    print("\nTesting SmartWebSocket initialization...")
    try:
        # This should work with 2 parameters
        ws = SmartWebSocket("dummy_feed_token", "dummy_client_id")
        print("✅ SmartWebSocket initialization with 2 parameters successful")
    except Exception as e:
        print(f"❌ SmartWebSocket initialization failed: {e}")
    
    # Test SmartWebSocketV2 if available
    try:
        print("\nTesting SmartWebSocketV2 initialization...")
        ws_v2 = SmartWebSocketV2(
            auth_token="dummy_auth",
            api_key="dummy_api",
            client_code="dummy_client",
            feed_token="dummy_feed"
        )
        print("✅ SmartWebSocketV2 initialization with 4 parameters successful")
    except Exception as e:
        print(f"❌ SmartWebSocketV2 initialization failed: {e}")
    
    print("\n🎉 All websocket tests completed!")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
