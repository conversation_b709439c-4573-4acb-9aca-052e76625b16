#!/usr/bin/env python3
"""
Historical Data Download Script for Options Trading System
Download NIFTY & BANK NIFTY options data from 01/07/2025 to 17/07/2025
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from agents.options_data_ingestion_agent import OptionsDataIngestionAgent

async def download_historical_data():
    """Download historical options data for specified date range"""
    
    # Define date range: 01/07/2025 to 17/07/2025 (current year)
    start_date = datetime(2025, 7, 1)
    end_date = datetime(2025, 7, 17)
    
    print(f"🚀 Starting historical data download for Options Trading System")
    print(f"📅 Date Range: {start_date.strftime('%d/%m/%Y')} to {end_date.strftime('%d/%m/%Y')}")
    print(f"🎯 Symbols: NIFTY & BANK NIFTY Options")
    print(f"⏰ Timeframes: 1min → 3min, 5min, 15min")
    print("=" * 70)
    
    # Initialize data ingestion agent
    agent = OptionsDataIngestionAgent()
    
    try:
        # Initialize agent
        print("🔧 Initializing Options Data Ingestion Agent...")
        success = await agent.initialize()
        if not success:
            print("❌ Failed to initialize agent")
            return False
        
        print("✅ Agent initialized successfully")
        
        # Download historical data only
        print("📊 Starting historical data download...")
        success = await agent.start(
            start_date=start_date,
            end_date=end_date,
            historical_only=True
        )
        
        if success:
            print("✅ Historical data download completed successfully!")
            print("\n📁 Data saved to:")
            print("   - Option/data/historical/1min/")
            print("   - Option/data/historical/3min/")
            print("   - Option/data/historical/5min/")
            print("   - Option/data/historical/15min/")
            print("\n🎯 Ready for feature engineering and AI model training!")
            return True
        else:
            print("❌ Historical data download failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during data download: {e}")
        return False
    finally:
        # Cleanup
        await agent.cleanup()

if __name__ == "__main__":
    # Run the download
    success = asyncio.run(download_historical_data())
    
    if success:
        print("\n🎉 Historical data download completed successfully!")
        print("🚀 Next steps:")
        print("   1. Run feature engineering: python main.py --agent feature_engineering")
        print("   2. Generate strategies: python main.py --agent strategy_generation")
        print("   3. Run backtesting: python main.py --agent backtesting")
        print("   4. Train AI models: python main.py --agent ai_training")
        sys.exit(0)
    else:
        print("\n💥 Historical data download failed!")
        print("🔍 Check logs for details and retry")
        sys.exit(1)
