"""
Option Data Downloader - June 2025 Monthly Expiry (Fixed Version)
Downloads 30 days of 5-minute candle data for Bank Nifty, Nifty indices and their June 2025 monthly expiry options
Fixed version without Unicode emojis for Windows compatibility
"""

import os
import sys
import time
import requests
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
import logging
import pymongo
from pymongo import MongoClient
import pytz

# SmartAPI imports
try:
    from SmartApi import SmartConnect
    import pyotp
except ImportError:
    print("SmartAPI not installed. Install with: pip install smartapi-python pyotp")
    sys.exit(1)

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))
except ImportError:
    print("python-dotenv not installed. Please install with: pip install python-dotenv")

# Set up logging without Unicode characters
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('option_data_download.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptionDataDownloader:
    """Downloads option and index data using SmartAPI"""
    
    def __init__(self):
        """Initialize the downloader"""
        # Get credentials from .env
        self.api_key = os.getenv('ANGEL_API_KEY')
        self.username = os.getenv('ANGEL_CLIENT_ID')
        self.password = os.getenv('ANGEL_CLIENT_PIN')
        self.totp_token = os.getenv('ANGEL_TOTP_KEY')
        
        # MongoDB connection for option_ai database
        self.mongo_url = os.getenv('MONGO_URL', 'mongodb://localhost:27017')
        self.db_name = 'option_ai'
        
        # SmartAPI objects
        self.smart_api = None
        self.auth_token = None
        self.feed_token = None
        
        # Connect to MongoDB
        self.client = MongoClient(self.mongo_url)
        self.db = self.client[self.db_name]
        
        # IST timezone
        self.ist_tz = pytz.timezone('Asia/Kolkata')
        
        # Token mappings
        self.tokens = {
            'BANKNIFTY': '********',
            'NIFTY': '********'
        }
        
        # June 2025 monthly expiry
        self.june_expiry = "31JUL2025"
        
        # Master data cache
        self.master_data = None
        
        logger.info(f"Initialized OptionDataDownloader for database: {self.db_name}")
        logger.info(f"Target expiry: {self.june_expiry}")
    
    def login(self):
        """Login to SmartAPI"""
        try:
            if not all([self.api_key, self.username, self.password, self.totp_token]):
                logger.error("Missing credentials in .env file")
                return False
            
            # Create SmartConnect object
            self.smart_api = SmartConnect(api_key=self.api_key)
            
            # Generate TOTP
            totp = pyotp.TOTP(self.totp_token).now()
            
            # Generate session
            data = self.smart_api.generateSession(self.username, self.password, totp)
            
            if not data or not data.get('status'):
                logger.error(f"Login failed: {data}")
                return False
            
            # Extract tokens
            self.auth_token = data['data']['jwtToken']
            self.feed_token = self.smart_api.getfeedToken()
            
            logger.info("SmartAPI login successful")
            return True
            
        except Exception as e:
            logger.error(f"Login error: {e}")
            return False
    
    def download_master_data(self):
        """Download SmartAPI master data"""
        try:
            logger.info("Downloading SmartAPI master data...")
            
            # SmartAPI master data URL
            url = "https://margincalculator.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json"
            
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Parse JSON data
            data = response.json()
            
            # Convert to simple list format for processing
            self.master_data = []
            for item in data:
                try:
                    # Parse expiry date
                    expiry_str = item.get('expiry', '')
                    if expiry_str:
                        try:
                            expiry_date = datetime.strptime(expiry_str, '%d%b%Y')
                        except:
                            expiry_date = None
                    else:
                        expiry_date = None
                    
                    # Parse strike price
                    strike = item.get('strike', '0')
                    try:
                        strike_price = float(strike) / 100  # Convert from paise
                    except:
                        strike_price = 0
                    
                    processed_item = {
                        'name': item.get('name', ''),
                        'symbol': item.get('symbol', ''),
                        'token': item.get('token', ''),
                        'instrumenttype': item.get('instrumenttype', ''),
                        'exch_seg': item.get('exch_seg', ''),
                        'expiry': expiry_str,
                        'expiry_date': expiry_date,
                        'strike': strike_price,
                        'lotsize': item.get('lotsize', '0')
                    }
                    self.master_data.append(processed_item)
                    
                except Exception as e:
                    continue
            
            logger.info(f"Downloaded {len(self.master_data)} instruments from SmartAPI")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading SmartAPI master data: {e}")
            return False
    
    def get_option_tokens(self, symbol: str, strike_range: tuple, days: int = 30) -> List[Dict]:
        """Get real option tokens for given strike range and June 2025 expiry"""
        try:
            logger.info(f"Fetching {symbol} option tokens for strikes {strike_range[0]}-{strike_range[1]} (June 2025 expiry)")
            
            if not self.master_data:
                logger.error("Master data not loaded. Call download_master_data() first.")
                return []
            
            # Filter for the specific symbol and June 2025 expiry
            symbol_options = []
            for item in self.master_data:
                if (item['name'] == symbol and 
                    item['instrumenttype'] == 'OPTIDX' and 
                    item['expiry'] == self.june_expiry and 
                    item['exch_seg'] == 'NFO'):
                    symbol_options.append(item)
            
            logger.info(f"Found {len(symbol_options)} {symbol} options for {self.june_expiry} expiry")
            
            # Filter by strike range
            start_strike, end_strike = strike_range
            strike_filtered = []
            for item in symbol_options:
                if start_strike <= item['strike'] <= end_strike:
                    strike_filtered.append(item)
            
            logger.info(f"Filtered to {len(strike_filtered)} options in strike range {start_strike}-{end_strike}")
            
            # Convert to our format
            options = []
            for item in strike_filtered:
                try:
                    # Parse option type from symbol
                    trading_symbol = item['symbol']
                    if trading_symbol.endswith('CE'):
                        option_type = 'CE'
                    elif trading_symbol.endswith('PE'):
                        option_type = 'PE'
                    else:
                        continue
                    
                    option = {
                        'symbol': symbol,
                        'strike_price': int(item['strike']),
                        'option_type': option_type,
                        'token': str(item['token']),
                        'trading_symbol': trading_symbol,
                        'exchange': 'NFO',
                        'expiry': self.june_expiry,
                        'lot_size': int(item.get('lotsize', 15 if symbol == 'BANKNIFTY' else 50))
                    }
                    options.append(option)
                    
                except Exception as e:
                    logger.debug(f"Error processing option row: {e}")
                    continue
            
            logger.info(f"Found {len(options)} valid {symbol} option tokens")
            
            # Group by option type for summary
            ce_count = len([o for o in options if o['option_type'] == 'CE'])
            pe_count = len([o for o in options if o['option_type'] == 'PE'])
            logger.info(f"   CE options: {ce_count}, PE options: {pe_count}")
            
            return options
            
        except Exception as e:
            logger.error(f"Error getting option tokens: {e}")
            return []
    
    def run_download(self):
        """Main download function"""
        try:
            logger.info("Starting Option Data Download for June 2025 Monthly Expiry")
            logger.info(f"Target Expiry: {self.june_expiry}")
            
            # Download SmartAPI master data first
            logger.info("Step 1: Downloading SmartAPI master data...")
            if not self.download_master_data():
                logger.error("Failed to download master data")
                return False
            
            # Login to SmartAPI
            logger.info("Step 2: Logging into SmartAPI...")
            if not self.login():
                logger.error("Failed to login to SmartAPI")
                return False
            
            logger.info("SUCCESS: All downloads completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error in main download: {e}")
            return False
        finally:
            # Close MongoDB connection
            if hasattr(self, 'client'):
                self.client.close()

def main():
    """Main function"""
    print("Option Data Downloader - June 2025 Monthly Expiry")
    print("Target: 26JUN2025 (Last Thursday of June)")
    print("Data: 30 days of 5-minute candles with volume")
    print("Strikes: Bank Nifty (55000-57500), Nifty (24400-25750)")
    print("Database: option_ai")
    print()
    
    downloader = OptionDataDownloader()
    success = downloader.run_download()
    
    if success:
        print("\nData download completed successfully!")
        print(f"Data stored in MongoDB database: option_ai")
        print(f"Expiry: {downloader.june_expiry}")
        print("\nNext steps:")
        print("   - Use simple_data_viewer.py to explore the data")
        print("   - Use check_data_health.py to verify data quality")
    else:
        print("\nData download failed. Check logs for details.")
        print("Common issues:")
        print("   - Check SmartAPI credentials in .env file")
        print("   - Ensure MongoDB is running")
        print("   - Verify internet connection")
        sys.exit(1)

if __name__ == "__main__":
    main()
