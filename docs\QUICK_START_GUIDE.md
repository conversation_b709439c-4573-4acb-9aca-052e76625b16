# 🚀 QUICK START GUIDE - NIFTY & BANK NIFTY OPTIONS TRADING SYSTEM

## 📋 Prerequisites

### System Requirements
- **OS**: Windows 10/11, Linux, macOS
- **Python**: 3.9+ (3.11 recommended)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space
- **GPU**: NVIDIA GPU with CUDA 11.8+ (optional but recommended)

### Trading Account Requirements
- **Broker**: Angel One (Angel Broking)
- **Segments**: NFO (Futures & Options) enabled
- **API Access**: SmartAPI credentials
- **Capital**: Minimum ₹50,000 recommended

## ⚡ Installation

### 1. <PERSON>lone and Setup
```bash
# Navigate to the Option directory
cd Option

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configure Environment
```bash
# Copy environment template
cp config/environment.yaml.example config/environment.yaml

# Edit with your credentials
nano config/environment.yaml
```

### 3. Environment Configuration
```yaml
# config/environment.yaml
smartapi:
  api_key: "your_api_key_here"
  client_id: "your_client_id_here"
  password: "your_password_here"
  totp_secret: "your_totp_secret_here"

trading:
  mode: "paper"  # paper or live
  capital: 100000
  risk_per_trade: 0.02
  max_positions: 5

data:
  storage_path: "data"
  retention_days: 30
  compression: "snappy"
```

## 🎯 Quick Start Commands

### Individual Agents
```bash
# 1. Data Ingestion - Download options data
python main.py --agent data_ingestion

# 2. Feature Engineering - Calculate Greeks and indicators
python main.py --agent feature_engineering

# 3. Strategy Generation - Create options strategies
python main.py --agent strategy_generation

# 4. Backtesting - Test strategies
python main.py --agent backtesting

# 5. AI Training - Train ML models
python main.py --agent ai_training

# 6. Market Monitoring - Monitor live markets
python main.py --agent market_monitoring

# 7. Signal Generation - Generate trading signals
python main.py --agent signal_generation

# 8. Risk Management - Monitor risk
python main.py --agent risk_management

# 9. Execution - Execute trades
python main.py --agent execution --demo

# 10. Performance Analysis - Analyze performance
python main.py --agent performance_analysis

# 11. LLM Interface - Natural language interface
python main.py --agent llm_interface

# 12. Strategy Evolution - Evolve strategies
python main.py --agent strategy_evolution
```

### Complete Workflows
```bash
# Full Pipeline - Complete end-to-end workflow
python main.py --workflow full_pipeline

# Training Pipeline - Data + Features + Strategies + Backtesting + AI
python main.py --workflow training_pipeline

# Live Trading - Real-time trading workflow
python main.py --workflow live_trading

# Data Pipeline - Data ingestion and feature engineering
python main.py --workflow data_pipeline

# Strategy Development - Strategy generation and optimization
python main.py --workflow strategy_development

# Research Pipeline - Data analysis and insights
python main.py --workflow options_research

# Multi-Timeframe Analysis - 1min→3min,5min,15min analysis
python main.py --workflow multi_timeframe_analysis
```

## 📈 Market Coverage

### Supported Options
- **NIFTY Options** (50-point strikes, weekly/monthly expiries)
- **BANK NIFTY Options** (100-point strikes, monthly expiries only)

### Multi-Timeframe Data
- **1-minute** base data download
- **3-minute** generated from 1-minute
- **5-minute** generated from 1-minute
- **15-minute** generated from 1-minute

### Data Storage
- **Historical**: `data/historical/{timeframe}/`
- **Live**: `data/live/{timeframe}/`
- **Features**: `data/features/{timeframe}/`

## 📊 First Run Example

### Step 1: Data Setup
```bash
# Download historical options data
python main.py --agent data_ingestion
```

### Step 2: Feature Engineering
```bash
# Calculate Greeks, volatility, and technical indicators
python main.py --agent feature_engineering
```

### Step 3: Strategy Generation
```bash
# Generate options trading strategies
python main.py --agent strategy_generation
```

### Step 4: Backtesting
```bash
# Backtest generated strategies
python main.py --agent backtesting
```

### Step 5: AI Training
```bash
# Train ML models on strategy performance
python main.py --agent ai_training
```

## 🎯 Demo Mode

### Paper Trading Setup
```bash
# Set environment to paper trading
export TRADING_MODE=paper

# Run live trading simulation
python main.py --workflow live_trading --demo
```

### Demo Commands
```bash
# Demo backtesting with sample data
python main.py --agent backtesting --demo

# Demo execution without real orders
python main.py --agent execution --demo

# Demo monitoring with simulated data
python main.py --agent market_monitoring --demo
```

## 📈 Monitoring and Logs

### Log Files
```bash
# Main system logs
tail -f logs/options_main.log

# Agent-specific logs
tail -f logs/options_data_ingestion.log
tail -f logs/options_backtesting.log
tail -f logs/options_execution.log
```

### Performance Monitoring
```bash
# View backtest results
ls data/backtest/

# View generated strategies
ls data/strategies/

# View performance reports
ls data/performance/
```

## 🛠️ Configuration Files

### Key Configuration Files
- `config/options_strategies.yaml` - Strategy definitions
- `config/options_symbols.json` - Symbol specifications
- `config/environment.yaml` - Environment settings
- `requirements.txt` - Python dependencies

### Data Directories
- `data/options_historical/` - Historical options data
- `data/options_live/` - Live market data
- `data/option_chains/` - Option chain data
- `data/features/` - Engineered features
- `data/strategies/` - Generated strategies
- `data/backtest/` - Backtesting results
- `data/models/` - Trained ML models

## 🚨 Troubleshooting

### Common Issues

#### 1. SmartAPI Connection Failed
```bash
# Check credentials
python -c "from smartapi import SmartConnect; print('SmartAPI installed')"

# Verify TOTP
python -c "import pyotp; print(pyotp.TOTP('your_secret').now())"
```

#### 2. Missing Dependencies
```bash
# Install missing packages
pip install py_vollib QuantLib-Python

# For TA-Lib on Windows
pip install TA-Lib-0.4.28-cp311-cp311-win_amd64.whl
```

#### 3. GPU Issues
```bash
# Check CUDA availability
python -c "import torch; print(torch.cuda.is_available())"

# Install CPU-only versions if needed
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

#### 4. Data Issues
```bash
# Clear cache and restart
rm -rf data/cache/
python main.py --agent data_ingestion
```

### Performance Optimization

#### For Better Performance
```bash
# Use GPU acceleration (if available)
export CUDA_VISIBLE_DEVICES=0

# Increase worker processes
export MAX_WORKERS=8

# Use faster compression
export COMPRESSION=lz4
```

#### Memory Optimization
```bash
# Reduce chunk size for large datasets
export CHUNK_SIZE=5000

# Enable memory monitoring
export MEMORY_MONITORING=true
```

## 📚 Next Steps

### 1. Customize Strategies
- Edit `config/options_strategies.yaml`
- Add your own strategy logic
- Adjust risk parameters

### 2. Optimize Performance
- Enable GPU acceleration
- Tune hyperparameters
- Optimize data processing

### 3. Live Trading
- Switch to live mode
- Start with small capital
- Monitor performance closely

### 4. Advanced Features
- Custom indicators
- Strategy evolution
- Portfolio optimization
- Risk management rules

## 🔗 Useful Links

- [Options Trading Guide](docs/OPTIONS_TRADING_GUIDE.md)
- [Strategy Documentation](docs/STRATEGY_DOCUMENTATION.md)
- [API Reference](docs/API_REFERENCE.md)
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md)

## ⚠️ Important Notes

1. **Paper Trading First**: Always test with paper trading before live trading
2. **Risk Management**: Never risk more than you can afford to lose
3. **Market Hours**: Options trading is only during market hours (9:15 AM - 3:30 PM IST)
4. **Regulatory Compliance**: Ensure compliance with SEBI regulations
5. **Tax Implications**: Consult a tax advisor for options trading tax implications

---

**🎯 Ready to Start Trading Options? Run your first command:**
```bash
python main.py --workflow training_pipeline
```
