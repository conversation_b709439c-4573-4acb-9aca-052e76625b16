#!/usr/bin/env python3
"""
🧬 Strategy Evolution Agent - Usage Example

This example demonstrates how to use the Options Strategy Evolution Agent
to automatically evolve and optimize trading strategies.
"""

import asyncio
import sys
import json
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from agents.options_strategy_evolution_agent import (
    OptionsStrategyEvolutionAgent,
    StrategyConfig,
    StrategyStatus,
    StrategyMetrics,
    MarketRegime,
    EvolutionReason
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StrategyEvolutionExample:
    """🧬 Example usage of Strategy Evolution Agent"""
    
    def __init__(self):
        self.agent = None
    
    async def run_example(self):
        """🚀 Run the complete example"""
        logger.info("🧬 Starting Strategy Evolution Agent Example...")
        
        try:
            # Step 1: Initialize the agent
            await self.initialize_agent()
            
            # Step 2: Create sample strategies
            await self.create_sample_strategies()
            
            # Step 3: Simulate performance data
            await self.simulate_performance_data()
            
            # Step 4: Demonstrate evolution process
            await self.demonstrate_evolution()
            
            # Step 5: Show regime adaptation
            await self.demonstrate_regime_adaptation()
            
            # Step 6: Display results
            await self.display_results()
            
        except Exception as e:
            logger.error(f"❌ Example failed: {e}")
            return False
        
        finally:
            if self.agent:
                await self.agent.cleanup()
        
        return True
    
    async def initialize_agent(self):
        """🚀 Initialize the Strategy Evolution Agent"""
        logger.info("🚀 Initializing Strategy Evolution Agent...")
        
        self.agent = OptionsStrategyEvolutionAgent()
        success = await self.agent.initialize()
        
        if success:
            logger.info("✅ Agent initialized successfully")
        else:
            raise Exception("Failed to initialize agent")
    
    async def create_sample_strategies(self):
        """📝 Create sample strategies for demonstration"""
        logger.info("📝 Creating sample strategies...")
        
        # Sample Strategy 1: Long Call
        long_call_strategy = StrategyConfig(
            strategy_id="LC_NIFTY_001",
            name="Long Call - Nifty Bullish",
            description="Simple long call strategy for bullish market outlook",
            parameters={
                "strike_selection": "otm_5_to_15_percent",
                "expiry_selection": "15_to_45_days",
                "max_position_size": 0.05
            },
            entry_conditions=[
                "rsi_14 < 40",
                "price > ema_20",
                "iv_rank < 60",
                "volume > avg_volume_20 * 1.2"
            ],
            exit_conditions=[
                "profit_target: 100%",
                "stop_loss: 50%",
                "time_decay: 7_days_to_expiry"
            ],
            risk_management={
                "stop_loss": 0.5,
                "take_profit": 1.0,
                "position_size": 0.05,
                "max_daily_loss": 0.02
            },
            market_outlook="bullish",
            volatility_outlook="neutral_to_high",
            timeframe="15min",
            status=StrategyStatus.ACTIVE,
            tags=["directional", "bullish", "long_call"]
        )
        
        # Sample Strategy 2: Iron Condor
        iron_condor_strategy = StrategyConfig(
            strategy_id="IC_BANKNIFTY_001",
            name="Iron Condor - Bank Nifty Neutral",
            description="Iron condor strategy for neutral market outlook",
            parameters={
                "strike_width": "200_points",
                "center_strike": "atm",
                "expiry_selection": "15_to_30_days"
            },
            entry_conditions=[
                "iv_rank > 60",
                "price_range_7d < 0.03",
                "volume_ratio < 1.5",
                "vix < 25"
            ],
            exit_conditions=[
                "profit_target: 50%",
                "stop_loss: 200%",
                "time_decay: 5_days_to_expiry"
            ],
            risk_management={
                "stop_loss": 2.0,
                "take_profit": 0.5,
                "position_size": 0.03,
                "max_positions": 3
            },
            market_outlook="neutral",
            volatility_outlook="expecting_decrease",
            timeframe="30min",
            status=StrategyStatus.ACTIVE,
            tags=["neutral", "volatility_selling", "iron_condor"]
        )
        
        # Sample Strategy 3: Long Straddle
        long_straddle_strategy = StrategyConfig(
            strategy_id="LS_NIFTY_001",
            name="Long Straddle - Volatility Play",
            description="Long straddle for high volatility events",
            parameters={
                "strike_selection": "atm",
                "expiry_selection": "7_to_21_days"
            },
            entry_conditions=[
                "iv_rank < 30",
                "upcoming_events",
                "realized_vol_20d < implied_vol",
                "price_near_support_resistance"
            ],
            exit_conditions=[
                "profit_target: 100%",
                "stop_loss: 50%",
                "volatility_expansion"
            ],
            risk_management={
                "stop_loss": 0.5,
                "take_profit": 1.0,
                "position_size": 0.04,
                "max_positions": 2
            },
            market_outlook="neutral",
            volatility_outlook="expecting_increase",
            timeframe="15min",
            status=StrategyStatus.ACTIVE,
            tags=["volatility_buying", "long_straddle", "event_driven"]
        )
        
        # Add strategies to registry
        strategies = [long_call_strategy, iron_condor_strategy, long_straddle_strategy]
        for strategy in strategies:
            self.agent.strategy_registry[strategy.strategy_id] = strategy
        
        await self.agent._save_strategy_registry()
        logger.info(f"✅ Created {len(strategies)} sample strategies")
    
    async def simulate_performance_data(self):
        """📊 Simulate performance data for strategies"""
        logger.info("📊 Simulating performance data...")
        
        # Simulate performance for each strategy over time
        strategies = list(self.agent.strategy_registry.keys())
        
        for strategy_id in strategies:
            performance_history = []
            
            # Generate 10 performance measurements over 30 days
            for i in range(10):
                timestamp = datetime.now() - timedelta(days=30-i*3)
                
                # Simulate different performance patterns
                if strategy_id == "LC_NIFTY_001":
                    # Declining performance (will trigger evolution)
                    roi = 0.08 - (i * 0.01)  # Declining from 8% to -2%
                    sharpe = 0.8 - (i * 0.1)  # Declining Sharpe
                    win_rate = 0.65 - (i * 0.03)  # Declining win rate
                    max_dd = 0.05 + (i * 0.01)  # Increasing drawdown
                
                elif strategy_id == "IC_BANKNIFTY_001":
                    # Stable performance
                    roi = 0.06 + (i * 0.002)  # Slightly improving
                    sharpe = 0.7 + (i * 0.02)  # Improving Sharpe
                    win_rate = 0.55 + (i * 0.01)  # Stable win rate
                    max_dd = 0.08 - (i * 0.002)  # Decreasing drawdown
                
                else:  # LS_NIFTY_001
                    # Volatile performance
                    roi = 0.04 + (i % 3 - 1) * 0.02  # Volatile returns
                    sharpe = 0.5 + (i % 2) * 0.2  # Volatile Sharpe
                    win_rate = 0.48 + (i % 3) * 0.05  # Volatile win rate
                    max_dd = 0.12 + (i % 2) * 0.03  # Volatile drawdown
                
                metrics = StrategyMetrics(
                    strategy_id=strategy_id,
                    roi=roi,
                    sharpe_ratio=sharpe,
                    win_rate=win_rate,
                    max_drawdown=max_dd,
                    expectancy=roi * win_rate,
                    profit_factor=1.2 + roi,
                    total_trades=20 + i * 2,
                    avg_trade_duration=2.5,
                    volatility=0.15,
                    calmar_ratio=roi / max_dd if max_dd > 0 else 0,
                    sortino_ratio=sharpe * 1.2,
                    timestamp=timestamp,
                    regime=MarketRegime.SIDEWAYS_LOW_VOL
                )
                
                performance_history.append(metrics)
            
            self.agent.performance_history[strategy_id] = performance_history
        
        await self.agent._save_performance_history()
        logger.info("✅ Performance data simulation completed")
    
    async def demonstrate_evolution(self):
        """🧪 Demonstrate the evolution process"""
        logger.info("🧪 Demonstrating strategy evolution...")
        
        # Detect underperforming strategies
        underperformers = await self.agent._detect_underperforming_strategies()
        
        if underperformers:
            logger.info(f"🔍 Found {len(underperformers)} underperforming strategies:")
            for strategy_id, issues in underperformers.items():
                logger.info(f"  - {strategy_id}: {', '.join(issues)}")
                
                # Flag for evolution
                await self.agent._flag_for_evolution(strategy_id, issues)
            
            # Process evolution queue
            await self.agent._process_evolution_queue()
            
            logger.info("✅ Evolution process completed")
        else:
            logger.info("ℹ️ No underperforming strategies found")
    
    async def demonstrate_regime_adaptation(self):
        """🌊 Demonstrate market regime adaptation"""
        logger.info("🌊 Demonstrating market regime adaptation...")
        
        # Simulate different market regimes
        regimes_to_test = [
            {
                'name': 'Trending Bull Market',
                'data': {
                    'volatility': 0.12,
                    'trend_strength': 0.8,
                    'volume_ratio': 1.3,
                    'price_change_1d': 0.025
                }
            },
            {
                'name': 'High Volatility Sideways',
                'data': {
                    'volatility': 0.28,
                    'trend_strength': 0.1,
                    'volume_ratio': 1.8,
                    'price_change_1d': 0.005
                }
            }
        ]
        
        for regime_test in regimes_to_test:
            detected_regime = await self.agent._detect_market_regime(regime_test['data'])
            logger.info(f"📊 {regime_test['name']}: Detected as {detected_regime.value}")
        
        logger.info("✅ Regime adaptation demonstration completed")
    
    async def display_results(self):
        """📊 Display evolution results"""
        logger.info("📊 Displaying evolution results...")
        
        logger.info("\n" + "="*60)
        logger.info("🧬 STRATEGY EVOLUTION RESULTS")
        logger.info("="*60)
        
        # Strategy Registry Summary
        total_strategies = len(self.agent.strategy_registry)
        active_strategies = sum(1 for s in self.agent.strategy_registry.values() 
                              if s.status == StrategyStatus.ACTIVE)
        experimental_strategies = sum(1 for s in self.agent.strategy_registry.values() 
                                    if s.status == StrategyStatus.EXPERIMENTAL)
        
        logger.info(f"📈 Total Strategies: {total_strategies}")
        logger.info(f"✅ Active Strategies: {active_strategies}")
        logger.info(f"🧪 Experimental Strategies: {experimental_strategies}")
        logger.info("")
        
        # Strategy Details
        for strategy_id, strategy in self.agent.strategy_registry.items():
            logger.info(f"🔹 {strategy.name} ({strategy_id})")
            logger.info(f"   Status: {strategy.status.value}")
            logger.info(f"   Parent: {strategy.parent_id or 'Original'}")
            logger.info(f"   Tags: {', '.join(strategy.tags)}")
            
            # Latest performance
            if strategy_id in self.agent.performance_history:
                latest_perf = self.agent.performance_history[strategy_id][-1]
                logger.info(f"   ROI: {latest_perf.roi:.2%}")
                logger.info(f"   Sharpe: {latest_perf.sharpe_ratio:.2f}")
                logger.info(f"   Win Rate: {latest_perf.win_rate:.2%}")
            logger.info("")
        
        # Evolution History
        if self.agent.evolution_history:
            logger.info("🧬 Recent Evolution Events:")
            for event in self.agent.evolution_history[-5:]:  # Last 5 events
                logger.info(f"   {event.timestamp.strftime('%Y-%m-%d %H:%M')} - {event.description}")
            logger.info("")
        
        logger.info("="*60)

async def main():
    """🚀 Main example function"""
    example = StrategyEvolutionExample()
    
    print("🧬 Options Strategy Evolution Agent - Example Usage")
    print("="*60)
    print("This example will:")
    print("1. Initialize the Strategy Evolution Agent")
    print("2. Create sample trading strategies")
    print("3. Simulate performance data")
    print("4. Demonstrate automatic evolution")
    print("5. Show market regime adaptation")
    print("6. Display comprehensive results")
    print("="*60)
    print()
    
    success = await example.run_example()
    
    if success:
        print("\n🎉 Strategy Evolution Agent example completed successfully!")
        print("\nNext steps:")
        print("- Check the generated files in data/strategy_evolution/")
        print("- Review the configuration in config/options_strategy_evolution_config.yaml")
        print("- Run the agent with: python main.py --agent strategy_evolution")
        return 0
    else:
        print("\n❌ Strategy Evolution Agent example failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
