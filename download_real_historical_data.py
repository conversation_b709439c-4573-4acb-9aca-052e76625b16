#!/usr/bin/env python3
"""
Real Historical Data Download Script using OpenChart
Download NIFTY & BANK NIFTY options data from 01/07/2025 to 17/07/2025
"""

import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta
from pathlib import Path
from openchart import NSEData
import time

def download_real_historical_data():
    """Download real historical options data using OpenChart"""
    
    print("🚀 Starting REAL historical data download using OpenChart")
    print("📅 Date Range: 01/07/2025 to 17/07/2025")
    print("🎯 Symbols: NIFTY & BANK NIFTY Options")
    print("⏰ Timeframes: 1min → 3min, 5min, 15min")
    print("=" * 70)
    
    # Initialize OpenChart NSE data
    nse = NSEData()
    
    try:
        # Download master data
        print("📊 Downloading NSE/NFO master data...")
        nse.download()
        print("✅ Master data downloaded successfully")
        
        # Date range
        start_date = datetime(2025, 7, 1)
        end_date = datetime(2025, 7, 17)
        
        # Data paths
        data_path = Path("data/historical")
        
        # Search for NIFTY options with correct expiry (17JUL25)
        print("\n🔍 Searching for NIFTY options (17JUL25)...")
        nifty_options = nse.search('NIFTY25JUL17', exchange='NFO')
        print(f"Found {len(nifty_options)} NIFTY options")
        
        # Search for BANKNIFTY options with correct expiry (31JUL25)
        print("🔍 Searching for BANKNIFTY options (31JUL25)...")
        banknifty_options = nse.search('BANKNIFTY25JUL31', exchange='NFO')
        print(f"Found {len(banknifty_options)} BANKNIFTY options")
        
        # Download data for selected options (limit to avoid overwhelming)
        all_data = []
        
        # Process NIFTY options (first 10 for demo)
        nifty_symbols = nifty_options['Symbol'].head(10).tolist() if len(nifty_options) > 0 else []
        print(f"\n📈 Processing {len(nifty_symbols)} NIFTY options...")
        
        for i, symbol in enumerate(nifty_symbols):
            try:
                print(f"  [{i+1}/{len(nifty_symbols)}] Downloading {symbol}...")
                
                # Download 1-minute data
                data = nse.historical(
                    symbol=symbol,
                    exchange='NFO',
                    start=start_date,
                    end=end_date,
                    interval='1m'
                )
                
                if data is not None and len(data) > 0:
                    # Convert to polars DataFrame
                    data_dict = {
                        'timestamp': data.index.tolist(),
                        'symbol': [symbol] * len(data),
                        'underlying': ['NIFTY'] * len(data),
                        'open': data['Open'].tolist(),
                        'high': data['High'].tolist(),
                        'low': data['Low'].tolist(),
                        'close': data['Close'].tolist(),
                        'volume': data['Volume'].tolist()
                    }
                    
                    # Parse symbol for strike and option type
                    # Format: NIFTY25JUL17XXXXX[CE/PE]
                    if 'CE' in symbol:
                        option_type = 'CE'
                        strike_str = symbol.replace('NIFTY25JUL17', '').replace('CE', '')
                    else:
                        option_type = 'PE'
                        strike_str = symbol.replace('NIFTY25JUL17', '').replace('PE', '')
                    
                    try:
                        strike_price = float(strike_str)
                    except:
                        strike_price = 0.0
                    
                    data_dict['strike_price'] = [strike_price] * len(data)
                    data_dict['option_type'] = [option_type] * len(data)
                    data_dict['expiry_date'] = ['17JUL2025'] * len(data)
                    
                    df = pl.DataFrame(data_dict)
                    all_data.append(df)
                    
                    print(f"    ✅ Downloaded {len(data)} records")
                else:
                    print(f"    ⚠️ No data available for {symbol}")
                
                # Small delay to avoid rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"    ❌ Error downloading {symbol}: {e}")
                continue
        
        # Process BANKNIFTY options (first 10 for demo)
        banknifty_symbols = banknifty_options['Symbol'].head(10).tolist() if len(banknifty_options) > 0 else []
        print(f"\n📊 Processing {len(banknifty_symbols)} BANKNIFTY options...")
        
        for i, symbol in enumerate(banknifty_symbols):
            try:
                print(f"  [{i+1}/{len(banknifty_symbols)}] Downloading {symbol}...")
                
                # Download 1-minute data
                data = nse.historical(
                    symbol=symbol,
                    exchange='NFO',
                    start=start_date,
                    end=end_date,
                    interval='1m'
                )
                
                if data is not None and len(data) > 0:
                    # Convert to polars DataFrame
                    data_dict = {
                        'timestamp': data.index.tolist(),
                        'symbol': [symbol] * len(data),
                        'underlying': ['BANKNIFTY'] * len(data),
                        'open': data['Open'].tolist(),
                        'high': data['High'].tolist(),
                        'low': data['Low'].tolist(),
                        'close': data['Close'].tolist(),
                        'volume': data['Volume'].tolist()
                    }
                    
                    # Parse symbol for strike and option type
                    # Format: BANKNIFTY25JUL31XXXXX[CE/PE]
                    if 'CE' in symbol:
                        option_type = 'CE'
                        strike_str = symbol.replace('BANKNIFTY25JUL31', '').replace('CE', '')
                    else:
                        option_type = 'PE'
                        strike_str = symbol.replace('BANKNIFTY25JUL31', '').replace('PE', '')
                    
                    try:
                        strike_price = float(strike_str)
                    except:
                        strike_price = 0.0
                    
                    data_dict['strike_price'] = [strike_price] * len(data)
                    data_dict['option_type'] = [option_type] * len(data)
                    data_dict['expiry_date'] = ['31JUL2025'] * len(data)
                    
                    df = pl.DataFrame(data_dict)
                    all_data.append(df)
                    
                    print(f"    ✅ Downloaded {len(data)} records")
                else:
                    print(f"    ⚠️ No data available for {symbol}")
                
                # Small delay to avoid rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"    ❌ Error downloading {symbol}: {e}")
                continue
        
        # Combine all data
        if all_data:
            print(f"\n💾 Combining and saving data...")
            combined_data = pl.concat(all_data)
            
            # Save 1-minute data
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename_1min = f"options_1min_{timestamp}.parquet"
            filepath_1min = data_path / "1min" / filename_1min
            
            combined_data.write_parquet(filepath_1min, compression="snappy")
            print(f"✅ Saved {combined_data.height} records to 1min/{filename_1min}")
            
            # Generate other timeframes
            generate_multi_timeframes(combined_data, data_path, timestamp)
            
            print(f"\n🎉 Real historical data download completed successfully!")
            print(f"📊 Total records: {combined_data.height}")
            print(f"📁 Data saved to Option/data/historical/")
            return True
        else:
            print("❌ No data was downloaded")
            return False
            
    except Exception as e:
        print(f"❌ Error during data download: {e}")
        return False

def generate_multi_timeframes(data: pl.DataFrame, base_path: Path, timestamp: str):
    """Generate 3min, 5min, 15min timeframes from 1min data"""
    
    timeframes = {"3min": 3, "5min": 5, "15min": 15}
    
    for tf_name, minutes in timeframes.items():
        try:
            print(f"📊 Generating {tf_name} timeframe...")
            
            resampled_data = (
                data
                .with_columns([
                    pl.col("timestamp").dt.truncate(f"{minutes}m").alias("timeframe_start")
                ])
                .group_by(["symbol", "underlying", "strike_price", "expiry_date", "option_type", "timeframe_start"])
                .agg([
                    pl.col("open").first().alias("open"),
                    pl.col("high").max().alias("high"),
                    pl.col("low").min().alias("low"),
                    pl.col("close").last().alias("close"),
                    pl.col("volume").sum().alias("volume")
                ])
                .rename({"timeframe_start": "timestamp"})
                .sort(["symbol", "timestamp"])
            )
            
            # Save resampled data
            filename = f"options_{tf_name}_{timestamp}.parquet"
            filepath = base_path / tf_name / filename
            
            resampled_data.write_parquet(filepath, compression="snappy")
            print(f"✅ Saved {resampled_data.height} records to {tf_name}/{filename}")
            
        except Exception as e:
            print(f"❌ Error generating {tf_name}: {e}")

if __name__ == "__main__":
    success = download_real_historical_data()
    
    if success:
        print("\n🚀 Next steps:")
        print("   1. Run feature engineering: python main.py --agent feature_engineering")
        print("   2. Generate strategies: python main.py --agent strategy_generation")
        print("   3. Run backtesting: python main.py --agent backtesting")
        print("   4. Train AI models: python main.py --agent ai_training")
    else:
        print("\n💥 Historical data download failed!")
        print("🔍 Check logs for details and retry")
