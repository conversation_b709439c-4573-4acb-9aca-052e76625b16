#!/usr/bin/env python3
"""
🧪 Test Script for Live Trading System
Tests the enhanced live trading system with all implemented features.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from run_live_trading import LiveTradingSystem

# Configure test logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_system_initialization():
    """Test system initialization and configuration loading."""
    logger.info("🧪 Testing system initialization...")
    
    # Test paper trading mode
    system = LiveTradingSystem(mode="paper")
    
    try:
        # Test initialization
        initialized = await system.initialize()
        if initialized:
            logger.info("✅ System initialization successful")
        else:
            logger.error("❌ System initialization failed")
            return False
        
        # Test configuration validation
        logger.info(f"📊 Configuration loaded: {len(system.config)} parameters")
        logger.info(f"💰 Virtual balance: ₹{system.virtual_balance:,.2f}")
        logger.info(f"🕐 Trading hours: {system.config.get('run_duration')}")
        
        # Test logging system
        system.log_trade({"test": "trade_log"})
        system.log_signal({"test": "signal_log"})
        system.log_performance({"test": "performance_log"})
        system.log_risk_event({"test": "risk_log"})
        system.log_error("Test error message")
        
        logger.info("✅ Logging system test successful")
        
        # Test configuration methods
        safety_checks = system.config.get('safety_checks', {})
        logger.info(f"🛡️ Safety checks configured: {len(safety_checks)} parameters")
        
        # Test LLM integration
        if system.llm_interface_agent:
            logger.info("🧠 LLM interface agent available")
        else:
            logger.warning("⚠️ LLM interface agent not available")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        # Cleanup
        try:
            await system.stop_trading()
        except:
            pass

async def test_paper_trading_simulation():
    """Test paper trading simulation features."""
    logger.info("🧪 Testing paper trading simulation...")
    
    system = LiveTradingSystem(mode="paper")
    
    try:
        await system.initialize()
        
        # Test signal processing
        test_signal = {
            'underlying': 'NIFTY',
            'strike_price': 18000,
            'option_type': 'CE',
            'expiry': '2024-01-25',
            'action': 'BUY',
            'lot_size': 1,
            'entry_price': 100.0,
            'stoploss': 80.0,
            'target': 150.0,
            'strategy_id': 'test_strategy',
            'confidence_score': 0.8
        }
        
        # Test paper trade execution
        await system._simulate_paper_trade_execution(test_signal)
        
        if system.open_virtual_positions:
            logger.info(f"✅ Paper trade executed: {len(system.open_virtual_positions)} positions")
            logger.info(f"💰 Updated balance: ₹{system.virtual_balance:,.2f}")
        else:
            logger.warning("⚠️ No positions created from test signal")
        
        # Test P&L update
        await system._update_virtual_positions_pnl()
        logger.info("✅ P&L update test successful")
        
        # Test performance metrics calculation
        metrics = await system._calculate_comprehensive_metrics()
        logger.info(f"📊 Performance metrics calculated: {len(metrics)} metrics")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Paper trading test failed: {e}")
        return False
    finally:
        try:
            await system.stop_trading()
        except:
            pass

async def test_safety_features():
    """Test safety and fail-safe features."""
    logger.info("🧪 Testing safety features...")
    
    system = LiveTradingSystem(mode="paper")
    
    try:
        await system.initialize()
        
        # Test safety checks
        safety_status = await system._perform_comprehensive_safety_checks()
        logger.info(f"🛡️ Safety checks completed: {len(safety_status)} status items")
        
        if safety_status.get("critical_issues"):
            logger.warning(f"⚠️ Critical issues detected: {len(safety_status['critical_issues'])}")
        else:
            logger.info("✅ No critical safety issues detected")
        
        if safety_status.get("warnings"):
            logger.info(f"⚠️ Warnings detected: {len(safety_status['warnings'])}")
        else:
            logger.info("✅ No safety warnings detected")
        
        # Test risk evaluation
        test_signal = {
            'underlying': 'NIFTY',
            'action': 'BUY',
            'entry_price': 100.0,
            'lot_size': 1
        }
        
        risk_eval = await system._evaluate_signal_risk(test_signal)
        logger.info(f"🔍 Risk evaluation: {risk_eval.get('approved', False)} - {risk_eval.get('reason', 'No reason')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Safety features test failed: {e}")
        return False
    finally:
        try:
            await system.stop_trading()
        except:
            pass

async def run_all_tests():
    """Run all test suites."""
    logger.info("🚀 Starting Live Trading System Tests...")
    
    tests = [
        ("System Initialization", test_system_initialization),
        ("Paper Trading Simulation", test_paper_trading_simulation),
        ("Safety Features", test_safety_features)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} - PASSED")
            else:
                logger.error(f"❌ {test_name} - FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Live Trading System is ready.")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Please review the issues.")
    
    return passed == total

if __name__ == "__main__":
    try:
        # Ensure required directories exist
        Path("logs").mkdir(exist_ok=True)
        Path("logs/trades").mkdir(exist_ok=True)
        Path("logs/signals").mkdir(exist_ok=True)
        Path("logs/risk").mkdir(exist_ok=True)
        Path("logs/performance").mkdir(exist_ok=True)
        Path("logs/errors").mkdir(exist_ok=True)
        Path("data").mkdir(exist_ok=True)
        Path("exports").mkdir(exist_ok=True)
        Path("exports/reports").mkdir(exist_ok=True)
        
        # Run tests
        success = asyncio.run(run_all_tests())
        
        if success:
            print("\n🎉 All tests completed successfully!")
            print("💡 You can now run the live trading system with:")
            print("   python run_live_trading.py --paper_trading")
        else:
            print("\n❌ Some tests failed. Please check the logs for details.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)
