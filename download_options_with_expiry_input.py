#!/usr/bin/env python3
"""
Interactive Options Data Downloader with Expiry Selection
Downloads real NIFTY & BANK NIFTY options data with user-selected expiry dates
Uses SmartAPI master data and allows interactive expiry selection
"""

import os
import sys
import time
import requests
import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import logging

# SmartAPI imports
try:
    from SmartApi import SmartConnect
    import pyotp
except ImportError:
    print("SmartAPI not installed. Install with: pip install smartapi-python pyotp")
    sys.exit(1)

# Load environment variables
try:
    from dotenv import load_dotenv
    # Load from root directory .env file
    env_path = Path(__file__).parent.parent / ".env"
    load_dotenv(env_path)
except ImportError:
    print("python-dotenv not installed. Please install with: pip install python-dotenv")

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InteractiveOptionsDownloader:
    """Downloads option data with interactive expiry selection"""
    
    def __init__(self):
        """Initialize the downloader"""
        # Get credentials from .env (using our existing variable names)
        self.api_key = os.getenv('SMARTAPI_API_KEY')
        self.username = os.getenv('SMARTAPI_USERNAME') 
        self.password = os.getenv('SMARTAPI_PASSWORD')
        self.totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
        
        # SmartAPI objects
        self.smart_api = None
        self.auth_token = None
        self.feed_token = None
        
        # Master data cache
        self.master_data = None
        
        # Data paths
        self.data_path = Path("data/historical")
        
        logger.info("Initialized InteractiveOptionsDownloader")
    
    def login(self):
        """Login to SmartAPI"""
        try:
            if not all([self.api_key, self.username, self.password, self.totp_token]):
                logger.error("Missing SmartAPI credentials in .env file")
                logger.error("Required: SMARTAPI_API_KEY, SMARTAPI_USERNAME, SMARTAPI_PASSWORD, SMARTAPI_TOTP_TOKEN")
                return False
            
            # Create SmartConnect object
            self.smart_api = SmartConnect(api_key=self.api_key)
            
            # Generate TOTP
            totp = pyotp.TOTP(self.totp_token).now()
            
            # Generate session
            data = self.smart_api.generateSession(self.username, self.password, totp)
            
            if not data or not data.get('status'):
                logger.error(f"SmartAPI login failed: {data}")
                return False
            
            # Extract tokens
            self.auth_token = data['data']['jwtToken']
            self.feed_token = self.smart_api.getfeedToken()
            
            logger.info("SmartAPI login successful")
            return True
            
        except Exception as e:
            logger.error(f"Login error: {e}")
            return False
    
    def download_master_data(self):
        """Download SmartAPI master data"""
        try:
            logger.info("Downloading SmartAPI master data...")
            
            # SmartAPI master data URL
            url = "https://margincalculator.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json"
            
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Parse JSON data
            data = response.json()
            
            # Convert to processed format
            self.master_data = []
            for item in data:
                try:
                    # Parse expiry date
                    expiry_str = item.get('expiry', '')
                    if expiry_str:
                        try:
                            expiry_date = datetime.strptime(expiry_str, '%d%b%Y')
                        except:
                            expiry_date = None
                    else:
                        expiry_date = None
                    
                    # Parse strike price (convert from paise to rupees)
                    strike = item.get('strike', '0')
                    try:
                        strike_price = float(strike) / 100
                    except:
                        strike_price = 0
                    
                    processed_item = {
                        'name': item.get('name', ''),
                        'symbol': item.get('symbol', ''),
                        'token': item.get('token', ''),
                        'instrumenttype': item.get('instrumenttype', ''),
                        'exch_seg': item.get('exch_seg', ''),
                        'expiry': expiry_str,
                        'expiry_date': expiry_date,
                        'strike': strike_price,
                        'lotsize': item.get('lotsize', '0')
                    }
                    self.master_data.append(processed_item)
                    
                except Exception as e:
                    continue
            
            logger.info(f"Downloaded {len(self.master_data)} instruments from SmartAPI")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading SmartAPI master data: {e}")
            return False
    
    def get_available_expiries(self, symbol: str) -> List[str]:
        """Get available expiry dates for a symbol"""
        try:
            if not self.master_data:
                return []
            
            expiries = set()
            for item in self.master_data:
                if (item['name'] == symbol and 
                    item['instrumenttype'] == 'OPTIDX' and 
                    item['exch_seg'] == 'NFO' and
                    item['expiry']):
                    expiries.add(item['expiry'])
            
            # Sort expiries by date
            sorted_expiries = []
            for expiry_str in expiries:
                try:
                    expiry_date = datetime.strptime(expiry_str, '%d%b%Y')
                    # Only include future expiries
                    if expiry_date.date() >= datetime.now().date():
                        sorted_expiries.append((expiry_date, expiry_str))
                except:
                    continue
            
            # Sort by date and return expiry strings
            sorted_expiries.sort(key=lambda x: x[0])
            return [expiry[1] for expiry in sorted_expiries[:10]]  # Limit to next 10 expiries
            
        except Exception as e:
            logger.error(f"Error getting expiries: {e}")
            return []
    
    def select_expiry_interactive(self, symbol: str) -> Optional[str]:
        """Interactive expiry selection"""
        try:
            expiries = self.get_available_expiries(symbol)
            
            if not expiries:
                print(f"❌ No expiries found for {symbol}")
                return None
            
            print(f"\n📅 Available expiry dates for {symbol}:")
            for i, expiry in enumerate(expiries, 1):
                # Parse and format date for display
                try:
                    expiry_date = datetime.strptime(expiry, '%d%b%Y')
                    formatted_date = expiry_date.strftime('%d %B %Y (%A)')
                    print(f"   {i}. {expiry} - {formatted_date}")
                except:
                    print(f"   {i}. {expiry}")
            
            while True:
                try:
                    choice = input(f"\nSelect expiry for {symbol} (1-{len(expiries)}): ").strip()
                    choice_num = int(choice)
                    
                    if 1 <= choice_num <= len(expiries):
                        selected_expiry = expiries[choice_num - 1]
                        print(f"✅ Selected expiry: {selected_expiry}")
                        return selected_expiry
                    else:
                        print(f"❌ Please enter a number between 1 and {len(expiries)}")
                        
                except ValueError:
                    print("❌ Please enter a valid number")
                except KeyboardInterrupt:
                    print("\n❌ Selection cancelled")
                    return None
                    
        except Exception as e:
            logger.error(f"Error in expiry selection: {e}")
            return None
    
    def get_option_contracts(self, symbol: str, expiry: str, strike_range: tuple = None) -> List[Dict]:
        """Get option contracts for given symbol and expiry"""
        try:
            if not self.master_data:
                logger.error("Master data not loaded")
                return []
            
            # Filter options for the symbol and expiry
            options = []
            for item in self.master_data:
                if (item['name'] == symbol and 
                    item['instrumenttype'] == 'OPTIDX' and 
                    item['expiry'] == expiry and 
                    item['exch_seg'] == 'NFO'):
                    
                    # Apply strike range filter if provided
                    if strike_range:
                        start_strike, end_strike = strike_range
                        if not (start_strike <= item['strike'] <= end_strike):
                            continue
                    
                    # Parse option type from symbol
                    trading_symbol = item['symbol']
                    if trading_symbol.endswith('CE'):
                        option_type = 'CE'
                    elif trading_symbol.endswith('PE'):
                        option_type = 'PE'
                    else:
                        continue
                    
                    option = {
                        'symbol': symbol,
                        'strike_price': item['strike'],
                        'option_type': option_type,
                        'token': str(item['token']),
                        'trading_symbol': trading_symbol,
                        'exchange': 'NFO',
                        'expiry': expiry,
                        'lot_size': int(item.get('lotsize', 15 if symbol == 'BANKNIFTY' else 25))
                    }
                    options.append(option)
            
            logger.info(f"Found {len(options)} {symbol} options for expiry {expiry}")
            
            # Group by option type for summary
            ce_count = len([o for o in options if o['option_type'] == 'CE'])
            pe_count = len([o for o in options if o['option_type'] == 'PE'])
            logger.info(f"   CE options: {ce_count}, PE options: {pe_count}")
            
            return options
            
        except Exception as e:
            logger.error(f"Error getting option contracts: {e}")
            return []

    def download_historical_data(self, contracts: List[Dict], start_date: datetime, end_date: datetime) -> Optional[pl.DataFrame]:
        """Download historical data for option contracts"""
        try:
            logger.info(f"Downloading historical data for {len(contracts)} contracts...")

            all_data = []
            successful_downloads = 0

            # Limit contracts to avoid overwhelming the API
            limited_contracts = contracts[:5]  # Process first 5 contracts
            logger.info(f"Processing {len(limited_contracts)} contracts to avoid rate limits")

            for i, contract in enumerate(limited_contracts):
                try:
                    logger.info(f"[{i+1}/{len(limited_contracts)}] Downloading {contract['trading_symbol']}...")

                    # Download historical data from SmartAPI
                    historical_data = self.smart_api.getCandleData({
                        "exchange": contract['exchange'],
                        "symboltoken": contract['token'],
                        "interval": "ONE_MINUTE",
                        "fromdate": start_date.strftime("%Y-%m-%d %H:%M"),
                        "todate": end_date.strftime("%Y-%m-%d %H:%M")
                    })

                    if historical_data and historical_data.get('status') and historical_data.get('data'):
                        candle_data = historical_data['data']

                        for candle in candle_data:
                            try:
                                # SmartAPI returns: [timestamp, open, high, low, close, volume]
                                timestamp_str = candle[0]
                                timestamp = datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S%z").replace(tzinfo=None)

                                data_row = {
                                    'timestamp': timestamp,
                                    'symbol': contract['trading_symbol'],
                                    'underlying': contract['symbol'],
                                    'strike_price': contract['strike_price'],
                                    'expiry_date': contract['expiry'],
                                    'option_type': contract['option_type'],
                                    'open': float(candle[1]),
                                    'high': float(candle[2]),
                                    'low': float(candle[3]),
                                    'close': float(candle[4]),
                                    'volume': int(candle[5]),
                                    'open_interest': 0  # OI not available in historical data
                                }
                                all_data.append(data_row)
                            except Exception as parse_error:
                                logger.warning(f"Failed to parse candle data: {parse_error}")
                                continue

                        successful_downloads += 1
                        logger.info(f"✅ Downloaded {len(candle_data)} candles for {contract['trading_symbol']}")
                    else:
                        logger.warning(f"⚠️ No data received for {contract['trading_symbol']}")

                    # Add delay to avoid rate limiting
                    time.sleep(3.0)

                except Exception as contract_error:
                    logger.warning(f"❌ Failed to download {contract['trading_symbol']}: {contract_error}")
                    time.sleep(1.0)
                    continue

            if all_data:
                df = pl.DataFrame(all_data)
                logger.info(f"✅ Downloaded {len(all_data)} total data points from {successful_downloads} contracts")
                return df
            else:
                logger.warning("⚠️ No data downloaded")
                return None

        except Exception as e:
            logger.error(f"Error downloading historical data: {e}")
            return None

    def save_data(self, data: pl.DataFrame, underlying: str, expiry: str):
        """Save data to parquet files"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # Save 1-minute data
            filename_1min = f"{underlying}_{expiry}_1min_{timestamp}.parquet"
            filepath_1min = self.data_path / "1min" / filename_1min
            data.write_parquet(filepath_1min, compression="snappy")
            logger.info(f"💾 Saved {data.height} records to 1min/{filename_1min}")

            # Generate other timeframes
            self.generate_multi_timeframes(data, underlying, expiry, timestamp)

        except Exception as e:
            logger.error(f"Error saving data: {e}")

    def generate_multi_timeframes(self, data: pl.DataFrame, underlying: str, expiry: str, timestamp: str):
        """Generate 3min, 5min, 15min timeframes from 1min data"""

        timeframes = {"3min": 3, "5min": 5, "15min": 15}

        for tf_name, minutes in timeframes.items():
            try:
                logger.info(f"📊 Generating {tf_name} timeframe...")

                resampled_data = (
                    data
                    .with_columns([
                        pl.col("timestamp").dt.truncate(f"{minutes}m").alias("timeframe_start")
                    ])
                    .group_by(["symbol", "underlying", "strike_price", "expiry_date", "option_type", "timeframe_start"])
                    .agg([
                        pl.col("open").first().alias("open"),
                        pl.col("high").max().alias("high"),
                        pl.col("low").min().alias("low"),
                        pl.col("close").last().alias("close"),
                        pl.col("volume").sum().alias("volume"),
                        pl.col("open_interest").last().alias("open_interest")
                    ])
                    .rename({"timeframe_start": "timestamp"})
                    .sort(["symbol", "timestamp"])
                )

                # Save resampled data
                filename = f"{underlying}_{expiry}_{tf_name}_{timestamp}.parquet"
                filepath = self.data_path / tf_name / filename
                resampled_data.write_parquet(filepath, compression="snappy")
                logger.info(f"💾 Saved {resampled_data.height} records to {tf_name}/{filename}")

            except Exception as e:
                logger.error(f"Error generating {tf_name}: {e}")

    def run_interactive_download(self):
        """Main interactive download function"""
        try:
            print("🚀 Interactive Options Data Downloader")
            print("📅 Downloads real NIFTY & BANK NIFTY options data")
            print("⏰ Date Range: 01/07/2025 to 17/07/2025")
            print("=" * 60)

            # Step 1: Download master data
            print("\n📊 Step 1: Downloading SmartAPI master data...")
            if not self.download_master_data():
                print("❌ Failed to download master data")
                return False

            # Step 2: Login to SmartAPI
            print("\n🔐 Step 2: Logging into SmartAPI...")
            if not self.login():
                print("❌ Failed to login to SmartAPI")
                return False

            # Step 3: Interactive expiry selection
            symbols = ['NIFTY', 'BANKNIFTY']
            selected_expiries = {}

            for symbol in symbols:
                print(f"\n🎯 Step 3.{symbols.index(symbol)+1}: Select expiry for {symbol}")
                expiry = self.select_expiry_interactive(symbol)
                if expiry:
                    selected_expiries[symbol] = expiry
                else:
                    print(f"❌ No expiry selected for {symbol}")
                    return False

            # Step 4: Download data for each symbol
            start_date = datetime(2025, 7, 1)
            end_date = datetime(2025, 7, 17)

            for symbol, expiry in selected_expiries.items():
                print(f"\n📈 Step 4.{list(selected_expiries.keys()).index(symbol)+1}: Downloading {symbol} data...")
                print(f"   Expiry: {expiry}")
                print(f"   Date Range: {start_date.strftime('%d/%m/%Y')} to {end_date.strftime('%d/%m/%Y')}")

                # Get option contracts
                contracts = self.get_option_contracts(symbol, expiry)

                if not contracts:
                    print(f"❌ No contracts found for {symbol} {expiry}")
                    continue

                # Download historical data
                data = self.download_historical_data(contracts, start_date, end_date)

                if data is not None:
                    # Save data
                    self.save_data(data, symbol, expiry)
                    print(f"✅ {symbol} data download completed!")
                else:
                    print(f"❌ {symbol} data download failed!")

            print(f"\n🎉 Interactive download completed!")
            print(f"📁 Data saved to: {self.data_path}")
            return True

        except Exception as e:
            logger.error(f"Error in interactive download: {e}")
            return False

def main():
    """Main function"""
    downloader = InteractiveOptionsDownloader()
    success = downloader.run_interactive_download()

    if success:
        print("\n🚀 Next steps:")
        print("   1. Run feature engineering: python main.py --agent feature_engineering")
        print("   2. Generate strategies: python main.py --agent strategy_generation")
        print("   3. Run backtesting: python main.py --agent backtesting")
        print("   4. Train AI models: python main.py --agent ai_training")
    else:
        print("\n💥 Download failed! Check logs for details.")

if __name__ == "__main__":
    main()
