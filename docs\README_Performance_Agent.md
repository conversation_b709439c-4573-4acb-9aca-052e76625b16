# 🚀 Enhanced Options Performance Analysis Agent

A comprehensive, Windows-optimized performance analytics system for options trading with advanced Greeks analysis, risk metrics, and AI-powered insights.

## ✨ Complete Feature Set

### ✅ **All 11 Core Features Implemented**

1. **📊 Trade-Level Performance Evaluation**
   - ROI (%), Absolute P&L, Holding time analysis
   - Target/SL/Manual exit tracking
   - Slippage vs expected price analysis
   - Confidence vs outcome correlation
   - Entry precision for CE/PE trades

2. **📈 Strategy-Wise Metrics Aggregation**
   - Win rate, Average ROI, Expectancy calculation
   - Sharpe, Sortino, Calmar ratios
   - Maximum drawdown, Capital efficiency
   - Signal-to-trade conversion rate

3. **🤖 Model Performance Monitoring**
   - Accuracy of predicted direction
   - Predicted vs actual ROI analysis
   - Confidence calibration curves
   - Drift detection with auto-alerts

4. **🌍 Regime-Based Strategy Evaluation**
   - Market regime performance analysis
   - Volatility regime optimization
   - Time-of-day success patterns
   - Expiry proximity behavior

5. **⚡ Risk Control Feedback**
   - Actual vs allowed capital at risk
   - Daily drawdown vs threshold monitoring
   - Trading pause effectiveness analysis
   - Risky signal filtering evaluation

6. **📋 Trade Session Summary Generator**
   - Daily/Weekly P&L summaries
   - Win/loss breakdown by strategy/time/source
   - Suggested strategy adjustments
   - LLM-ready insights

7. **🚨 Anomaly & Error Detection**
   - Trade without signal detection
   - Early exit identification
   - Unrealistic ROI spike alerts
   - Execution failure analysis

8. **📊 Historical Performance Visualization**
   - P&L curves per strategy/date/model
   - Sharpe timeline tracking
   - Hour-of-day success heatmaps
   - Strike-wise profitability analysis

9. **🏷️ AI Training Labels Engine**
   - is_profitable, was_model_correct labels
   - expected_vs_actual_roi_gap calculation
   - signal_quality_tag classification
   - suitable_for_training assessment

10. **🔄 Strategy Evolution Feedback Loop**
    - Parameter tuning recommendations
    - Strategy deprecation analysis
    - Multi-strategy fusion suggestions
    - Meta-performance logging

11. **📱 Enhanced Dashboard Output**
    - Comprehensive JSON/Excel exports
    - Real-time Windows notifications
    - Interactive performance metrics
    - Advanced risk analytics

### 🔥 **Advanced Features**

#### 💎 **Greeks P&L Attribution**
- **Delta P&L**: Directional movement impact
- **Gamma P&L**: Acceleration effects
- **Theta P&L**: Time decay analysis
- **Vega P&L**: Volatility sensitivity
- **Rho P&L**: Interest rate effects

#### 📊 **Advanced Risk Metrics**
- **Monte Carlo Simulations**: 10,000 iterations
- **Stress Testing**: Market crash, volatility spike, rate shock
- **VaR/CVaR**: 95% and 99% confidence levels
- **Sortino Ratio**: Downside deviation focus
- **Calmar Ratio**: Return vs max drawdown

#### 🎨 **Enhanced Visualizations**
- **Performance Heatmaps**: Hour-of-day, strategy vs regime
- **P&L Curves**: Overall, strategy-wise, Greeks attribution
- **Drawdown Analysis**: Peak-to-trough tracking
- **Strike Profitability**: Moneyness analysis

#### 📤 **Export Capabilities**
- **Excel Reports**: Multi-sheet comprehensive analysis
- **JSON Exports**: API-ready data format
- **Google Sheets**: Integration ready

#### 🗣️ **Natural Language Interface**
- Query performance in plain English
- AI-powered insight generation
- Conversational analytics

#### 🔔 **Windows Notifications**
- Real-time alert system
- Threshold-based warnings
- Performance milestone notifications

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- Windows 10/11 (optimized)

### Install Dependencies
```bash
pip install -r requirements_performance_agent.txt
```

### Key Dependencies
- `polars>=0.20.0` - High-performance data processing
- `polars-talib>=0.1.5` - Technical indicators
- `scipy>=1.10.0` - Advanced statistics
- `openpyxl>=3.1.0` - Excel export
- `win10toast>=0.9` - Windows notifications

## 🚀 Quick Start

### Basic Usage
```python
import asyncio
from agents.options_performance_analysis_agent import OptionsPerformanceAnalysisAgent

async def main():
    agent = OptionsPerformanceAnalysisAgent()
    await agent.initialize()
    await agent.start()

if __name__ == "__main__":
    asyncio.run(main())
```

### Run Tests
```bash
python test_options_performance_agent.py
```

## 📁 Data Structure

### Required Data Files
```
data/
├── trades/
│   └── completed_trades.parquet
├── greeks/
│   └── options_greeks.parquet
└── historical/
    └── underlying_data.parquet
```

### Trade Data Schema
```python
{
    "trade_id": str,
    "strategy_id": str,
    "entry_time": datetime,
    "exit_time": datetime,
    "entry_price": float,
    "exit_price": float,
    "quantity": int,
    "option_type": str,  # "CALL" or "PUT"
    "strike_price": float,
    "underlying_entry_price": float,
    "underlying_exit_price": float,
    "signal_confidence": float,
    "market_regime": str,
    "volatility_regime": str,
    # ... additional fields
}
```

## 🎯 Natural Language Queries

Ask questions in plain English:
- "What is my win rate?"
- "Which is my best strategy?"
- "How much did theta contribute to my PnL?"
- "What is my maximum drawdown?"
- "Show me my worst trade"

## 📊 Dashboard Output

### Enhanced Dashboard Features
- **Summary Metrics**: Total trades, P&L, win rate
- **Performance Metrics**: Sharpe, Sortino, Calmar ratios
- **Risk Metrics**: Max drawdown, VaR, CVaR
- **Heatmaps**: Time-based and regime-based analysis
- **P&L Curves**: Multiple visualization formats
- **Monte Carlo**: Risk simulation results
- **Stress Testing**: Scenario analysis
- **AI Insights**: Automated recommendations

### Export Formats
- **Excel**: Multi-sheet comprehensive reports
- **JSON**: API-ready structured data
- **Parquet**: High-performance storage

## ⚙️ Configuration

### Config Options
```python
config = {
    'analysis_interval': 300,  # 5 minutes
    'metrics': ['sharpe', 'sortino', 'calmar', 'max_drawdown', 'var', 'cvar'],
    'monte_carlo_simulations': 10000,
    'stress_test_scenarios': {
        'market_crash': -0.20,
        'volatility_spike': 2.0,
        'interest_rate_shock': 0.02
    },
    'notification_settings': {
        'enable_windows_notifications': True,
        'alert_thresholds': {
            'max_drawdown': 0.10,
            'daily_loss': 0.05,
            'model_drift': 0.15
        }
    }
}
```

## 🔔 Alerts & Notifications

### Automatic Alerts
- **High Drawdown**: When exceeding threshold
- **Model Drift**: Performance degradation
- **Risk Breach**: VaR/CVaR violations
- **Anomaly Detection**: Unusual trading patterns

### Windows Notifications
Real-time toast notifications for critical alerts.

## 🧪 Testing

Comprehensive test suite with sample data:
- 100 sample trades across 4 strategies
- Greeks data simulation
- Historical price data generation
- All feature validation

## 📈 Performance

### Optimizations
- **Polars**: 150x faster than pandas
- **Vectorized Operations**: Efficient calculations
- **Lazy Evaluation**: Memory optimization
- **Parallel Processing**: Multi-core utilization

## 🤝 Integration

### SmartAPI Compatibility
Ready for integration with Angel One SmartAPI:
- Trade data ingestion
- Real-time Greeks updates
- Live performance monitoring

### LLM Integration
- Natural language insights
- Automated report generation
- Conversational analytics

## 📝 License

MIT License - See LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the test suite output
2. Review log files in `logs/`
3. Validate data schema compliance
4. Ensure all dependencies are installed

---

**🎉 Ready for Production Use!**

This enhanced agent provides institutional-grade performance analytics with all 11 core features plus advanced capabilities for comprehensive options trading analysis.
