# 🚀 Enhanced Options Performance Analysis Agent - Dependencies
# Windows-optimized requirements for comprehensive performance analytics

# Core Data Processing
polars>=0.20.0
polars-talib>=0.1.5
numpy>=1.24.0
scipy>=1.10.0

# Data Export and Visualization
pandas>=2.0.0
openpyxl>=3.1.0
xlsxwriter>=3.0.0

# Windows Notifications
win10toast>=0.9; platform_system=="Windows"

# Async and Utilities
asyncio-mqtt>=0.13.0
aiofiles>=23.0.0
pydantic>=2.0.0

# Logging and Configuration
loguru>=0.7.0
pyyaml>=6.0

# Optional: Google Sheets Integration
gspread>=5.7.0
google-auth>=2.16.0
google-auth-oauthlib>=0.8.0
google-auth-httplib2>=0.1.0

# Optional: Advanced Analytics
scikit-learn>=1.3.0
statsmodels>=0.14.0

# Development and Testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# SmartAPI Integration (if needed)
smartapi-python>=1.4.8
pyotp>=2.8.0
websocket-client>=1.5.0
pycryptodome>=3.17.0

# Additional Utilities
python-dateutil>=2.8.0
pytz>=2023.3
