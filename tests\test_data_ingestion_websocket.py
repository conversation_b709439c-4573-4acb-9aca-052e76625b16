#!/usr/bin/env python3
"""
Test script to verify data ingestion agent websocket initialization
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.append(os.getcwd())

async def test_data_ingestion_websocket():
    try:
        print("Testing Data Ingestion Agent websocket initialization...")
        
        # Import the agent
        from agents.options_data_ingestion_agent import OptionsDataIngestionAgent
        
        # Create agent instance
        agent = OptionsDataIngestionAgent()
        
        # Initialize the agent
        print("Initializing agent...")
        success = await agent.initialize()
        
        if success:
            print("✅ Agent initialization successful")
            
            # Test websocket initialization specifically
            print("Testing websocket initialization...")
            await agent._initialize_websocket()
            
            if agent.smart_websocket:
                print("✅ Websocket initialization successful")
                print(f"Websocket type: {type(agent.smart_websocket)}")
            else:
                print("⚠️ Websocket initialization failed, but this is expected without real credentials")
            
        else:
            print("❌ Agent initialization failed")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_data_ingestion_websocket())
