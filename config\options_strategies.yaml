# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 NIFTY & BANK NIFTY OPTIONS TRADING STRATEGIES CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Comprehensive options strategies for Indian markets with risk management

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 DIRECTIONAL STRATEGIES
# ═══════════════════════════════════════════════════════════════════════════════
directional_strategies:
  long_call:
    name: "Long Call"
    description: "Bullish directional strategy with unlimited upside"
    market_outlook: "bullish"
    volatility_outlook: "neutral_to_high"
    max_risk: "premium_paid"
    max_reward: "unlimited"
    break_even: "strike + premium"
    time_decay: "negative"
    parameters:
      strike_selection: "otm_5_to_15_percent"
      expiry_selection: "15_to_45_days"
      entry_conditions:
        - "rsi_14 < 40"
        - "underlying_above_ema_20"
        - "iv_rank < 50"
      exit_conditions:
        - "profit_target: 100%"
        - "stop_loss: 50%"
        - "time_decay: 7_days_to_expiry"

  long_put:
    name: "Long Put"
    description: "Bearish directional strategy with high profit potential"
    market_outlook: "bearish"
    volatility_outlook: "neutral_to_high"
    max_risk: "premium_paid"
    max_reward: "strike - premium"
    break_even: "strike - premium"
    time_decay: "negative"
    parameters:
      strike_selection: "otm_5_to_15_percent"
      expiry_selection: "15_to_45_days"
      entry_conditions:
        - "rsi_14 > 60"
        - "underlying_below_ema_20"
        - "iv_rank < 50"
      exit_conditions:
        - "profit_target: 100%"
        - "stop_loss: 50%"
        - "time_decay: 7_days_to_expiry"

  covered_call:
    name: "Covered Call"
    description: "Income generation strategy for stock holdings"
    market_outlook: "neutral_to_slightly_bullish"
    volatility_outlook: "high"
    max_risk: "stock_price - premium_received"
    max_reward: "strike - stock_price + premium"
    break_even: "stock_price - premium"
    time_decay: "positive"
    parameters:
      strike_selection: "otm_5_to_10_percent"
      expiry_selection: "15_to_30_days"
      entry_conditions:
        - "iv_rank > 50"
        - "underlying_in_range"
      exit_conditions:
        - "profit_target: 50%"
        - "roll_up: underlying_above_strike"

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 VOLATILITY STRATEGIES
# ═══════════════════════════════════════════════════════════════════════════════
volatility_strategies:
  long_straddle:
    name: "Long Straddle"
    description: "Profit from large moves in either direction"
    market_outlook: "neutral"
    volatility_outlook: "expecting_increase"
    max_risk: "total_premium_paid"
    max_reward: "unlimited"
    break_even: ["strike - total_premium", "strike + total_premium"]
    time_decay: "negative"
    parameters:
      strike_selection: "atm"
      expiry_selection: "15_to_45_days"
      entry_conditions:
        - "iv_rank < 30"
        - "upcoming_events"
        - "low_realized_volatility"
      exit_conditions:
        - "profit_target: 100%"
        - "stop_loss: 50%"
        - "volatility_expansion"

  long_strangle:
    name: "Long Strangle"
    description: "Lower cost volatility play with wider break-evens"
    market_outlook: "neutral"
    volatility_outlook: "expecting_increase"
    max_risk: "total_premium_paid"
    max_reward: "unlimited"
    break_even: ["put_strike - total_premium", "call_strike + total_premium"]
    time_decay: "negative"
    parameters:
      strike_selection: "otm_5_to_10_percent"
      expiry_selection: "15_to_45_days"
      entry_conditions:
        - "iv_rank < 25"
        - "upcoming_earnings"
        - "technical_breakout_setup"
      exit_conditions:
        - "profit_target: 100%"
        - "stop_loss: 50%"
        - "volatility_crush"

  short_straddle:
    name: "Short Straddle"
    description: "Profit from low volatility and time decay"
    market_outlook: "neutral"
    volatility_outlook: "expecting_decrease"
    max_risk: "unlimited"
    max_reward: "total_premium_received"
    break_even: ["strike - total_premium", "strike + total_premium"]
    time_decay: "positive"
    parameters:
      strike_selection: "atm"
      expiry_selection: "15_to_30_days"
      entry_conditions:
        - "iv_rank > 70"
        - "high_realized_volatility"
        - "range_bound_market"
      exit_conditions:
        - "profit_target: 50%"
        - "stop_loss: 200%"
        - "volatility_expansion"

  iron_condor:
    name: "Iron Condor"
    description: "Limited risk/reward range-bound strategy"
    market_outlook: "neutral"
    volatility_outlook: "expecting_decrease"
    max_risk: "spread_width - net_credit"
    max_reward: "net_credit_received"
    break_even: ["put_spread_lower + net_credit", "call_spread_upper - net_credit"]
    time_decay: "positive"
    parameters:
      strike_selection: "otm_10_to_20_percent"
      spread_width: "200_to_500_points"
      expiry_selection: "15_to_45_days"
      entry_conditions:
        - "iv_rank > 50"
        - "range_bound_market"
        - "low_directional_bias"
      exit_conditions:
        - "profit_target: 50%"
        - "stop_loss: 200%"
        - "manage_at_21_dte"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 SPREAD STRATEGIES
# ═══════════════════════════════════════════════════════════════════════════════
spread_strategies:
  bull_call_spread:
    name: "Bull Call Spread"
    description: "Limited risk bullish strategy"
    market_outlook: "moderately_bullish"
    volatility_outlook: "neutral"
    max_risk: "net_debit_paid"
    max_reward: "spread_width - net_debit"
    break_even: "lower_strike + net_debit"
    time_decay: "neutral_to_negative"
    parameters:
      strike_selection: "itm_and_otm"
      spread_width: "200_to_500_points"
      expiry_selection: "15_to_45_days"
      entry_conditions:
        - "bullish_technical_setup"
        - "support_level_hold"
        - "rsi_oversold_recovery"
      exit_conditions:
        - "profit_target: 75%"
        - "stop_loss: 50%"
        - "underlying_below_lower_strike"

  bear_put_spread:
    name: "Bear Put Spread"
    description: "Limited risk bearish strategy"
    market_outlook: "moderately_bearish"
    volatility_outlook: "neutral"
    max_risk: "net_debit_paid"
    max_reward: "spread_width - net_debit"
    break_even: "higher_strike - net_debit"
    time_decay: "neutral_to_negative"
    parameters:
      strike_selection: "itm_and_otm"
      spread_width: "200_to_500_points"
      expiry_selection: "15_to_45_days"
      entry_conditions:
        - "bearish_technical_setup"
        - "resistance_level_rejection"
        - "rsi_overbought_decline"
      exit_conditions:
        - "profit_target: 75%"
        - "stop_loss: 50%"
        - "underlying_above_higher_strike"

  calendar_spread:
    name: "Calendar Spread"
    description: "Time decay strategy with volatility play"
    market_outlook: "neutral"
    volatility_outlook: "front_month_high_back_month_low"
    max_risk: "net_debit_paid"
    max_reward: "varies_with_volatility"
    break_even: "complex_calculation"
    time_decay: "positive_for_front_month"
    parameters:
      strike_selection: "atm_or_slightly_otm"
      expiry_selection: "front_15_30_back_45_60_days"
      entry_conditions:
        - "front_month_iv > back_month_iv"
        - "neutral_market_expectation"
        - "low_correlation_between_months"
      exit_conditions:
        - "front_month_expiry_approach"
        - "volatility_convergence"
        - "underlying_move_away_from_strike"

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 COMPLEX STRATEGIES
# ═══════════════════════════════════════════════════════════════════════════════
complex_strategies:
  iron_butterfly:
    name: "Iron Butterfly"
    description: "High probability neutral strategy"
    market_outlook: "neutral"
    volatility_outlook: "expecting_decrease"
    max_risk: "spread_width - net_credit"
    max_reward: "net_credit_received"
    break_even: ["center_strike - net_credit", "center_strike + net_credit"]
    time_decay: "positive"
    parameters:
      strike_selection: "atm_center"
      wing_width: "200_to_400_points"
      expiry_selection: "15_to_30_days"
      entry_conditions:
        - "iv_rank > 60"
        - "tight_range_market"
        - "high_probability_setup"
      exit_conditions:
        - "profit_target: 50%"
        - "stop_loss: 200%"
        - "manage_at_50_percent_time"

  ratio_call_spread:
    name: "Ratio Call Spread"
    description: "Unequal leg spread for income generation"
    market_outlook: "neutral_to_moderately_bullish"
    volatility_outlook: "expecting_decrease"
    max_risk: "unlimited_above_upper_break_even"
    max_reward: "varies_with_ratio"
    break_even: ["lower_strike + net_credit", "complex_upper_calculation"]
    time_decay: "positive"
    parameters:
      ratio: "1_to_2_or_1_to_3"
      strike_selection: "itm_and_otm"
      expiry_selection: "15_to_45_days"
      entry_conditions:
        - "high_iv_environment"
        - "range_bound_expectation"
        - "careful_risk_management"
      exit_conditions:
        - "profit_target: 50%"
        - "stop_loss: defined_level"
        - "volatility_expansion"

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ STRATEGY SELECTION CRITERIA
# ═══════════════════════════════════════════════════════════════════════════════
selection_criteria:
  market_conditions:
    trending_up:
      preferred_strategies: ["long_call", "bull_call_spread", "covered_call"]
      avoid_strategies: ["long_put", "bear_put_spread"]
    
    trending_down:
      preferred_strategies: ["long_put", "bear_put_spread", "protective_put"]
      avoid_strategies: ["long_call", "bull_call_spread"]
    
    range_bound:
      preferred_strategies: ["iron_condor", "iron_butterfly", "short_straddle", "calendar_spread"]
      avoid_strategies: ["long_straddle", "long_strangle"]
    
    high_volatility:
      preferred_strategies: ["short_straddle", "iron_condor", "covered_call"]
      avoid_strategies: ["long_straddle", "long_strangle"]
    
    low_volatility:
      preferred_strategies: ["long_straddle", "long_strangle", "calendar_spread"]
      avoid_strategies: ["short_straddle", "iron_condor"]

  risk_management:
    position_sizing:
      max_risk_per_trade: 0.02  # 2% of capital
      max_portfolio_risk: 0.10  # 10% of capital
      correlation_limit: 0.7    # Maximum correlation between positions
    
    exit_rules:
      profit_targets:
        directional: 100%
        volatility: 100%
        spreads: 75%
        income: 50%
      
      stop_losses:
        directional: 50%
        volatility: 50%
        spreads: 50%
        income: 200%
      
      time_based:
        dte_management: 7  # Days to expiry
        theta_threshold: -0.05
        delta_adjustment: 0.30

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 PERFORMANCE METRICS
# ═══════════════════════════════════════════════════════════════════════════════
performance_targets:
  minimum_metrics:
    win_rate: 0.60
    profit_factor: 1.50
    sharpe_ratio: 1.00
    max_drawdown: 0.15
    
  optimal_metrics:
    win_rate: 0.70
    profit_factor: 2.00
    sharpe_ratio: 1.50
    max_drawdown: 0.10

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 MARKET-SPECIFIC SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
market_settings:
  nifty:
    lot_size: 50
    tick_size: 0.05
    margin_multiplier: 1.0
    volatility_range: [0.12, 0.35]
    
  banknifty:
    lot_size: 15
    tick_size: 0.05
    margin_multiplier: 1.2
    volatility_range: [0.15, 0.40]
