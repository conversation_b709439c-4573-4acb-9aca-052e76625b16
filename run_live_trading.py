#!/usr/bin/env python3
"""
🚦 Live Trading File - Automated Options Trading System

This script orchestrates the entire live trading process, supporting both
paper trading and real money trading via Angel One SmartAPI.

Features:
- Launch Modes: Paper (--paper_trading) and Live (--real)
- System Initialization: Loads all agents, strategies, AI models, and configs.
- Real-Time Market Data Listener: Connects to Angel One WebSocket, streams tick data.
- Trade Signal Trigger Loop: Generates market snapshots, evaluates strategies,
  invokes AI, generates signals, and forwards to Risk Management and Execution.
- Paper Trading Mode Logic: Maintains virtual balance, executes mock trades,
  records P&L, and generates reports.
- Real Trading Mode Logic: Authenticates with SmartAPI, places actual orders,
  tracks fills, slippage, margin, and reports real P&L.
- Live Position & Trade Management: Displays capital, open trades, auto-square off.
- Logging & Reporting: Logs signals, trades, risk evaluations, execution status,
  and stores data for AI feedback and historical analysis.
- Daily Summary Generator: Creates end-of-day summaries.
- CLI Interface: Configurable via command-line flags or YAML.
- Safety & Fail-Safes: Detects API disconnection, rapid capital drop,
  consecutive SL hits, extreme market events.
- LLM Features: Provides natural language explanations and interactive queries.
"""

import asyncio
import argparse
import logging
import yaml
import json
import os
from datetime import datetime, time, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import polars as pl
import aiofiles

# Enhanced logging configuration will be set up in the class
logger = logging.getLogger(__name__)

# Import Agents
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent
from agents.options_signal_generation_agent import OptionsSignalGenerationAgent
from agents.options_risk_management_agent import OptionsRiskManagementAgent
from agents.options_execution_agent import OptionsExecutionAgent
from agents.options_performance_analysis_agent import OptionsPerformanceAnalysisAgent
from agents.options_ai_training_agent import OptionsAITrainingAgent
from agents.options_llm_interface_agent import OptionsLLMInterfaceAgent
from agents.options_data_ingestion_agent import OptionsDataIngestionAgent

class LiveTradingSystem:
    """
    Orchestrates the live trading process, integrating various agents.
    """
    def __init__(self, mode: str, config_path: str = "config/live_trading_config.yaml"):
        self.mode = mode # "paper" or "real"
        self.config_path = config_path
        self.config = {}
        self.is_running = False
        self.start_time = datetime.now()
        self.end_time = None # Will be set from config

        # Agents
        self.market_monitoring_agent: Optional[OptionsMarketMonitoringAgent] = None
        self.signal_generation_agent: Optional[OptionsSignalGenerationAgent] = None
        self.risk_management_agent: Optional[OptionsRiskManagementAgent] = None
        self.execution_agent: Optional[OptionsExecutionAgent] = None
        self.performance_analysis_agent: Optional[OptionsPerformanceAnalysisAgent] = None
        self.ai_training_agent: Optional[OptionsAITrainingAgent] = None
        self.llm_interface_agent: Optional[OptionsLLMInterfaceAgent] = None
        self.data_ingestion_agent: Optional[OptionsDataIngestionAgent] = None

        # Paper Trading Specifics
        self.virtual_balance = 100000.0 # Starting with ₹1,00,000
        self.open_virtual_positions = {} # {tradingsymbol: {qty, entry_price, sl, tp, pnl_history, current_pnl, last_ltp}}
        self.virtual_trades_log = [] # List of mock trade records
        self.consecutive_sl_hits = 0 # For safety checks

        # Logging setup will be done after config loading
        self.loggers = {}

        logger.info(f"🚦 Live Trading System initialized in {self.mode.upper()} mode.")

    def _setup_comprehensive_logging(self):
        """Setup comprehensive logging system with multiple specialized loggers."""
        try:
            # Get logging config
            log_config = self.config.get("logging", {})
            log_level = self.config.get("log_level", "INFO")

            # Create formatters
            detailed_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
            )
            simple_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )

            # Setup main logger
            main_logger = logging.getLogger("live_trading")
            main_logger.setLevel(getattr(logging, log_level.upper()))

            # Clear existing handlers
            main_logger.handlers.clear()

            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(simple_formatter)
            main_logger.addHandler(console_handler)

            # Main log file handler
            main_file_handler = self._create_rotating_file_handler(
                "logs/live_trading.log", detailed_formatter, log_config
            )
            main_logger.addHandler(main_file_handler)

            # Setup specialized loggers
            self._setup_specialized_loggers(detailed_formatter, simple_formatter, log_config)

            logger.info("📝 Comprehensive logging system initialized")

        except Exception as e:
            logger.error(f"❌ Error setting up logging: {e}")

    def _create_rotating_file_handler(self, filename: str, formatter: logging.Formatter,
                                    log_config: Dict[str, Any]) -> logging.Handler:
        """Create a rotating file handler with configuration."""
        from logging.handlers import RotatingFileHandler

        max_bytes = log_config.get("max_file_size_mb", 100) * 1024 * 1024  # Convert MB to bytes
        backup_count = log_config.get("backup_count", 5)

        handler = RotatingFileHandler(
            filename,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        handler.setFormatter(formatter)
        return handler

    def _setup_specialized_loggers(self, detailed_formatter: logging.Formatter,
                                 simple_formatter: logging.Formatter, log_config: Dict[str, Any]):
        """Setup specialized loggers for different components."""

        # Trade logger
        trade_logger = logging.getLogger("trades")
        trade_logger.setLevel(logging.INFO)
        trade_handler = self._create_rotating_file_handler(
            "logs/trades/trades.log", detailed_formatter, log_config
        )
        trade_logger.addHandler(trade_handler)
        self.loggers["trades"] = trade_logger

        # Signal logger
        signal_logger = logging.getLogger("signals")
        signal_logger.setLevel(logging.INFO)
        signal_handler = self._create_rotating_file_handler(
            "logs/signals/signals.log", detailed_formatter, log_config
        )
        signal_logger.addHandler(signal_handler)
        self.loggers["signals"] = signal_logger

        # Risk logger
        risk_logger = logging.getLogger("risk")
        risk_logger.setLevel(logging.INFO)
        risk_handler = self._create_rotating_file_handler(
            "logs/risk/risk.log", detailed_formatter, log_config
        )
        risk_logger.addHandler(risk_handler)
        self.loggers["risk"] = risk_logger

        # Performance logger
        performance_logger = logging.getLogger("performance")
        performance_logger.setLevel(logging.INFO)
        performance_handler = self._create_rotating_file_handler(
            "logs/performance/performance.log", simple_formatter, log_config
        )
        performance_logger.addHandler(performance_handler)
        self.loggers["performance"] = performance_logger

        # Error logger
        error_logger = logging.getLogger("errors")
        error_logger.setLevel(logging.ERROR)
        error_handler = self._create_rotating_file_handler(
            "logs/errors/errors.log", detailed_formatter, log_config
        )
        error_logger.addHandler(error_handler)
        self.loggers["errors"] = error_logger

    def log_trade(self, trade_data: Dict[str, Any]):
        """Log trade information to specialized trade logger."""
        if "trades" in self.loggers:
            self.loggers["trades"].info(f"TRADE: {json.dumps(trade_data)}")

    def log_signal(self, signal_data: Dict[str, Any]):
        """Log signal information to specialized signal logger."""
        if "signals" in self.loggers:
            self.loggers["signals"].info(f"SIGNAL: {json.dumps(signal_data)}")

    def log_risk_event(self, risk_data: Dict[str, Any]):
        """Log risk events to specialized risk logger."""
        if "risk" in self.loggers:
            self.loggers["risk"].warning(f"RISK: {json.dumps(risk_data)}")

    def log_performance(self, performance_data: Dict[str, Any]):
        """Log performance metrics to specialized performance logger."""
        if "performance" in self.loggers:
            self.loggers["performance"].info(f"PERFORMANCE: {json.dumps(performance_data)}")

    def log_error(self, error_message: str, error_data: Optional[Dict[str, Any]] = None):
        """Log errors to specialized error logger."""
        if "errors" in self.loggers:
            if error_data:
                self.loggers["errors"].error(f"ERROR: {error_message} - Data: {json.dumps(error_data)}")
            else:
                self.loggers["errors"].error(f"ERROR: {error_message}")

    async def initialize(self):
        """
        Enhanced initialization with comprehensive logging and agent setup.
        """
        try:
            # Load configuration first
            await self._load_config()

            # Setup comprehensive logging system
            self._setup_comprehensive_logging()

            # Set trading hours
            self._set_trading_hours()

            logger.info("✅ Initializing all agents...")

            # Initialize agents based on configuration
            await self._initialize_agents()

            logger.info("✅ All agents initialized successfully.")

            # Log initialization completion
            self.log_performance({
                "event": "system_initialized",
                "mode": self.mode,
                "capital": self.virtual_balance,
                "timestamp": datetime.now().isoformat()
            })

            return True
        except Exception as e:
            error_msg = f"Failed to initialize Live Trading System: {e}"
            logger.error(f"❌ [ERROR] {error_msg}")
            self.log_error(error_msg, {"mode": self.mode, "config_path": self.config_path})
            return False

    async def _initialize_agents(self):
        """Initialize all trading agents based on configuration."""
        agent_config = self.config.get("agents", {})

        # Initialize Data Ingestion Agent (must be first for real-time data)
        self.data_ingestion_agent = OptionsDataIngestionAgent()
        await self.data_ingestion_agent.initialize()
        logger.info("✅ Data Ingestion Agent initialized")

        # Initialize Market Monitoring Agent
        if agent_config.get("market_monitoring", {}).get("enabled", True):
            self.market_monitoring_agent = OptionsMarketMonitoringAgent()
            await self.market_monitoring_agent.initialize()
            logger.info("✅ Market Monitoring Agent initialized")

        # Initialize Signal Generation Agent
        if agent_config.get("signal_generation", {}).get("enabled", True):
            self.signal_generation_agent = OptionsSignalGenerationAgent()
            await self.signal_generation_agent.initialize()
            logger.info("✅ Signal Generation Agent initialized")

        # Initialize Risk Management Agent
        if agent_config.get("risk_management", {}).get("enabled", True):
            self.risk_management_agent = OptionsRiskManagementAgent()
            await self.risk_management_agent.initialize()
            logger.info("✅ Risk Management Agent initialized")

        # Initialize Execution Agent (dry_run based on mode)
        if agent_config.get("execution", {}).get("enabled", True):
            self.execution_agent = OptionsExecutionAgent(dry_run=(self.mode == "paper"))
            await self.execution_agent.initialize()
            logger.info("✅ Execution Agent initialized")

        # Initialize Performance Analysis Agent
        if agent_config.get("performance_analysis", {}).get("enabled", True):
            self.performance_analysis_agent = OptionsPerformanceAnalysisAgent()
            await self.performance_analysis_agent.initialize()
            logger.info("✅ Performance Analysis Agent initialized")

        # Initialize AI Training Agent (usually disabled during live trading)
        if agent_config.get("ai_training", {}).get("enabled", False):
            self.ai_training_agent = OptionsAITrainingAgent()
            await self.ai_training_agent.initialize()
            logger.info("✅ AI Training Agent initialized")

        # Initialize LLM Interface Agent
        if agent_config.get("llm_interface", {}).get("enabled", True):
            self.llm_interface_agent = OptionsLLMInterfaceAgent()
            await self.llm_interface_agent.initialize()
            logger.info("✅ LLM Interface Agent initialized")

    async def _load_config(self):
        """Enhanced configuration loading with validation and defaults."""
        try:
            if Path(self.config_path).exists():
                async with aiofiles.open(self.config_path, mode="r", encoding="utf-8") as f:
                    content = await f.read()
                    self.config = yaml.safe_load(content)
                logger.info(f"⚙️ Configuration loaded from {self.config_path}")
            else:
                logger.warning(f"⚠️ Config file not found at {self.config_path}. Using default configurations.")
                self.config = self._get_default_config()

            # Validate and apply configuration
            self._validate_config()
            self._apply_config_overrides()

            # Override capital for paper trading if specified in config
            if self.mode == "paper" and "capital" in self.config:
                self.virtual_balance = float(self.config["capital"])
                logger.info(f"📝 Paper trading capital set to ₹{self.virtual_balance:,.2f} from config.")

        except Exception as e:
            logger.error(f"❌ [ERROR] Error loading config: {e}")
            raise

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if config file is not found."""
        return {
            "mode": "paper",
            "capital": 100000.0,
            "enable_llm_summary": True,
            "run_duration": "09:15 to 15:15",
            "signal_check_interval_seconds": 5,
            "eod_square_off_time": "15:20",
            "llm_query_interval": 900,
            "market_settings": {
                "nifty": {"lot_size": 50, "tick_size": 0.05, "margin_required": 15000},
                "banknifty": {"lot_size": 25, "tick_size": 0.05, "margin_required": 25000}
            },
            "paper_trading": {
                "brokerage_rate": 0.0003,
                "slippage_rate": 0.001,
                "enable_realistic_fills": True,
                "simulate_market_impact": True
            },
            "safety_checks": {
                "api_disconnection_threshold_minutes": 5,
                "max_drawdown_percent": 0.10,
                "consecutive_sl_hits_limit": 3,
                "vix_spike_threshold": 0.25,
                "max_positions_per_underlying": 3,
                "max_capital_per_trade_percent": 0.05
            },
            "agents": {
                "market_monitoring": {"enabled": True},
                "signal_generation": {"enabled": True},
                "risk_management": {"enabled": True},
                "execution": {"enabled": True},
                "performance_analysis": {"enabled": True},
                "ai_training": {"enabled": False},
                "llm_interface": {"enabled": True}
            },
            "logging": {
                "file_rotation": True,
                "max_file_size_mb": 100,
                "backup_count": 5
            },
            "windows": {
                "use_emoji": True,
                "console_encoding": "utf-8",
                "file_encoding": "utf-8"
            }
        }

    def _validate_config(self):
        """Validate configuration parameters."""
        # Validate capital
        if self.config.get("capital", 0) <= 0:
            raise ValueError("Capital must be greater than 0")

        # Validate trading hours
        run_duration = self.config.get("run_duration", "09:15 to 15:15")
        if " to " not in run_duration:
            raise ValueError("Invalid run_duration format. Expected 'HH:MM to HH:MM'")

        # Validate safety checks
        safety_checks = self.config.get("safety_checks", {})
        if safety_checks.get("max_drawdown_percent", 0) <= 0 or safety_checks.get("max_drawdown_percent", 0) > 1:
            raise ValueError("max_drawdown_percent must be between 0 and 1")

        # Validate mode
        if self.config.get("mode") not in ["paper", "real"]:
            raise ValueError("Mode must be either 'paper' or 'real'")

        logger.info("✅ Configuration validation passed")

    def _apply_config_overrides(self):
        """Apply any runtime configuration overrides."""
        # Override mode if specified in constructor
        if hasattr(self, 'mode') and self.mode:
            self.config["mode"] = self.mode
            logger.info(f"🔧 Mode overridden to: {self.mode}")

        # Ensure required directories exist
        self._create_required_directories()

    def _create_required_directories(self):
        """Create required directories based on configuration."""
        directories = [
            "logs", "logs/trades", "logs/signals", "logs/errors", "logs/risk", "logs/performance",
            "data", "data/llm_insights", "exports", "exports/reports",
            "backups"
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

        logger.info("📁 Required directories created/verified")

    def _set_trading_hours(self):
        """Set daily trading start and end times based on config."""
        run_duration_str = self.config.get("run_duration", "09:15 to 15:15")
        start_time_str, end_time_str = run_duration_str.split(" to ")
        
        today = datetime.now().date()
        self.start_time = datetime.combine(today, datetime.strptime(start_time_str, "%H:%M").time())
        self.end_time = datetime.combine(today, datetime.strptime(end_time_str, "%H:%M").time())
        
        eod_square_off_time_str = self.config.get("eod_square_off_time", "15:20")
        self.eod_square_off_time = datetime.combine(today, datetime.strptime(eod_square_off_time_str, "%H:%M").time())

        logger.info(f"⏰ Trading hours set: {self.start_time.strftime('%H:%M')} to {self.end_time.strftime('%H:%M')}. EOD Square-off at {self.eod_square_off_time.strftime('%H:%M')}")

    async def start_trading(self):
        """
        Starts the main trading loop.
        """
        self.is_running = True
        logger.info(f"▶️ Live Trading System started in {self.mode.upper()} mode. Current Balance: ₹{self.virtual_balance:,.2f}")

        # Start background tasks for agents
        await asyncio.gather(
            self._data_ingestion_loop(),  # Start real-time data feed first
            self._market_data_listener_loop(),
            self._signal_trigger_loop(),
            self._position_management_loop(),
            self._safety_and_failsafe_loop(),
            self._daily_summary_loop(),
            self._llm_interface_loop(),
            self._paper_trading_pnl_update_loop(), # New loop for paper trading P&L
            self._performance_monitoring_loop() # New performance monitoring loop
        )

    async def _data_ingestion_loop(self):
        """
        Starts the real-time data ingestion from websocket feeds.
        """
        logger.info("📡 Starting Real-Time Data Ingestion Loop...")
        if self.data_ingestion_agent:
            await self.data_ingestion_agent.start()
        else:
            logger.error("❌ Data Ingestion Agent not initialized")

    async def _market_data_listener_loop(self):
        """
        Continuously listens for real-time market data and feeds it to agents.
        """
        logger.info("📊 Starting Market Data Listener Loop...")
        await self.market_monitoring_agent.start() # This agent handles its own internal loops
        # The market_monitoring_agent will continuously update its internal cache
        # and generate summaries/alerts. Other agents will query it for latest data.

    async def _signal_trigger_loop(self):
        """
        Periodically triggers signal generation and trade execution.
        """
        logger.info("⚡ Starting Signal Trigger Loop...")
        signal_interval = self.config.get("signal_check_interval_seconds", 5)
        while self.is_running:
            now = datetime.now()
            if self.start_time <= now <= self.end_time:
                try:
                    logger.info(f"✨ Triggering signal generation at {now.strftime('%H:%M:%S')}...")
                    
                    # 1. Generate fresh market snapshot (features) - MarketMonitoringAgent provides this
                    # The MarketMonitoringAgent continuously updates its internal state.
                    # We can query it for the latest market data.
                    latest_market_data = self.market_monitoring_agent.market_data_cache # Access the cache directly
                    latest_regime_state = self.market_monitoring_agent.market_regime_state # Access regime state

                    # 2. Evaluate active strategies & Invoke AI prediction engine & Generate signal
                    # The SignalGenerationAgent's start method will run a cycle and produce signals
                    # We need to ensure it can access the latest market data.
                    # For now, we'll assume SignalGenerationAgent can fetch its own data or we pass it.
                    # Given its current implementation, it loads data internally.
                    
                    # Trigger a single signal generation cycle
                    # We need to modify SignalGenerationAgent to have a public method for a single cycle
                    # For now, we'll call its internal method directly if possible, or adapt.
                    # Assuming _run_signal_generation_cycle is made public or a new method is added.
                    
                    # Temporarily set continuous_mode to False for a single run
                    original_continuous_mode = self.signal_generation_agent.config['continuous_mode']
                    self.signal_generation_agent.config['continuous_mode'] = False
                    self.signal_generation_agent.config['run_once'] = True # Ensure it runs once

                    await self.signal_generation_agent._run_signal_generation_cycle()
                    
                    # Restore original continuous_mode
                    self.signal_generation_agent.config['continuous_mode'] = original_continuous_mode
                    self.signal_generation_agent.config['run_once'] = False

                    # Retrieve generated signals (assuming SignalGenerationAgent saves them or returns them)
                    # For now, we'll assume signals are saved to data/signals and we can read the latest.
                    latest_signals_path = Path("data/signals")
                    latest_signal_file = None
                    if latest_signals_path.exists():
                        files = list(latest_signals_path.glob("final_consolidated_multi_timeframe_*.parquet"))
                        if files:
                            latest_signal_file = max(files, key=lambda x: x.stat().st_mtime)
                            signals_df = pl.read_parquet(latest_signal_file)
                            logger.info(f"📈 Loaded {signals_df.height} latest signals from {latest_signal_file.name}")
                            
                            for signal in signals_df.iter_rows(named=True):
                                # Log signal processing
                                self.log_signal({
                                    "timestamp": datetime.now().isoformat(),
                                    "signal": dict(signal),
                                    "mode": self.mode
                                })

                                # 3. Forward to Risk Management Agent for evaluation
                                risk_evaluation = await self._evaluate_signal_risk(signal)

                                if not risk_evaluation.get("approved", False):
                                    logger.warning(f"❌ Signal rejected by risk management: {risk_evaluation.get('reason', 'Unknown')}")
                                    self.log_risk_event({
                                        "event": "signal_rejected",
                                        "signal": dict(signal),
                                        "reason": risk_evaluation.get('reason'),
                                        "timestamp": datetime.now().isoformat()
                                    })
                                    continue

                                # 4. Execute trade based on mode
                                logger.info(f"Processing signal: {signal.get('strategy_id')} - {signal.get('action')} {signal.get('underlying')}")

                                if self.mode == "paper":
                                    await self._simulate_paper_trade_execution(signal)
                                else:
                                    await self._execute_real_trade(signal, risk_evaluation)
                        else:
                            logger.info("No new signals found to process.")
                    else:
                        logger.warning("Signals directory not found. Cannot retrieve signals.")

                except Exception as e:
                    logger.error(f"❌ [ERROR] Error in signal trigger loop: {e}")
            else:
                logger.info(f"😴 Outside trading hours. Current time: {now.strftime('%H:%M:%S')}")
            
            await asyncio.sleep(signal_interval)

    async def _simulate_paper_trade_execution(self, signal: Dict[str, Any]):
        """
        Enhanced paper trading simulation with better price fetching and position tracking.
        """
        try:
            # Extract signal details
            underlying = signal.get('underlying', 'NIFTY')
            strike_price = signal.get('strike_price', 0)
            option_type = signal.get('option_type', 'CE')
            expiry = signal.get('expiry', '')
            action = signal.get('action', 'BUY').upper()
            lot_size = signal.get('lot_size', 1)

            # Create trading symbol
            tradingsymbol = f"{underlying}_{strike_price}{option_type}_{expiry}"

            # Calculate quantity based on lot size and market settings
            market_lot_size = self.config.get('market_settings', {}).get(underlying.lower(), {}).get('lot_size', 25)
            quantity = lot_size * market_lot_size

            # Get entry price with enhanced logic
            entry_price = await self._get_simulated_entry_price(signal, tradingsymbol)
            if not entry_price:
                logger.warning(f"⚠️ Could not determine entry price for {tradingsymbol}. Skipping paper trade.")
                return

            # Extract SL/TP levels
            stoploss = signal.get('stoploss')
            target = signal.get('target')

            # Calculate trade cost including simulated brokerage
            brokerage_rate = self.config.get('paper_trading', {}).get('brokerage_rate', 0.0003)  # 0.03%
            trade_cost = entry_price * quantity
            brokerage = trade_cost * brokerage_rate
            total_cost = trade_cost + brokerage

            # Execute paper trade based on action
            if action == "BUY":
                await self._execute_paper_buy(tradingsymbol, quantity, entry_price, total_cost, stoploss, target, signal)
            elif action == "SELL":
                await self._execute_paper_sell(tradingsymbol, quantity, entry_price, total_cost, stoploss, target, signal)
            else:
                logger.warning(f"⚠️ Unknown action '{action}' for paper trade. Skipping.")

        except Exception as e:
            logger.error(f"❌ Error in paper trade execution: {e}")

    async def _get_simulated_entry_price(self, signal: Dict[str, Any], tradingsymbol: str) -> Optional[float]:
        """
        Enhanced entry price simulation with multiple fallback methods.
        """
        entry_price = signal.get('entry_price')

        if entry_price:
            return float(entry_price)

        # Try to get live price from market monitoring agent
        underlying = signal.get('underlying', 'NIFTY')
        strike_price = signal.get('strike_price', 0)
        option_type = signal.get('option_type', 'CE')
        expiry = signal.get('expiry', '')

        try:
            # Method 1: Get from market data cache
            latest_data = self.market_monitoring_agent.market_data_cache.get('1min', {}).get(underlying, {})
            if 'option_chain' in latest_data and not latest_data['option_chain'].is_empty():
                option_contract = latest_data['option_chain'].filter(
                    (pl.col('strike_price') == strike_price) &
                    (pl.col('option_type') == option_type) &
                    (pl.col('expiry_date') == expiry)
                ).select('close').head(1)

                if not option_contract.is_empty():
                    price = option_contract['close'].item()
                    logger.info(f"� Live price for {tradingsymbol}: ₹{price:.2f}")
                    return float(price)

            # Method 2: Simulate price based on underlying price and basic option pricing
            underlying_price = await self._get_underlying_price(underlying)
            if underlying_price:
                simulated_price = self._simulate_option_price(underlying_price, strike_price, option_type)
                logger.info(f"🎯 Simulated price for {tradingsymbol}: ₹{simulated_price:.2f}")
                return simulated_price

        except Exception as e:
            logger.error(f"❌ Error getting entry price for {tradingsymbol}: {e}")

        return None

    async def _get_underlying_price(self, underlying: str) -> Optional[float]:
        """Get current underlying asset price."""
        try:
            latest_data = self.market_monitoring_agent.market_data_cache.get('1min', {}).get(underlying, {})
            if 'ohlcv' in latest_data and not latest_data['ohlcv'].is_empty():
                return float(latest_data['ohlcv']['close'].tail(1).item())
        except Exception as e:
            logger.error(f"❌ Error getting underlying price for {underlying}: {e}")
        return None

    def _simulate_option_price(self, underlying_price: float, strike_price: float, option_type: str) -> float:
        """
        Basic option price simulation using intrinsic value + time value.
        This is a simplified model for paper trading.
        """
        if option_type == 'CE':  # Call option
            intrinsic_value = max(0, underlying_price - strike_price)
        else:  # Put option
            intrinsic_value = max(0, strike_price - underlying_price)

        # Add some time value (simplified)
        time_value = max(1.0, intrinsic_value * 0.1)  # 10% of intrinsic or minimum ₹1

        return intrinsic_value + time_value

    async def _execute_paper_buy(self, tradingsymbol: str, quantity: int, entry_price: float,
                                total_cost: float, stoploss: Optional[float], target: Optional[float],
                                signal: Dict[str, Any]):
        """Execute paper BUY trade."""
        if self.virtual_balance >= total_cost:
            self.virtual_balance -= total_cost
            self.open_virtual_positions[tradingsymbol] = {
                "qty": quantity,
                "entry_price": entry_price,
                "action": "BUY",
                "stoploss": stoploss,
                "target": target,
                "pnl_history": [],
                "current_pnl": 0.0,
                "last_ltp": entry_price,
                "entry_time": datetime.now(),
                "strategy_id": signal.get('strategy_id'),
                "confidence": signal.get('confidence_score'),
                "underlying": signal.get('underlying'),
                "strike_price": signal.get('strike_price'),
                "option_type": signal.get('option_type'),
                "expiry": signal.get('expiry')
            }
            self._log_paper_trade(tradingsymbol, "BUY", quantity, entry_price, "OPEN")
            logger.info(f"✅ PAPER BUY: {quantity} of {tradingsymbol} @ ₹{entry_price:.2f}. Balance: ₹{self.virtual_balance:,.2f}")
            await self._send_notification(f"📝 Paper Trade: BUY {quantity} of {tradingsymbol} @ ₹{entry_price:.2f}", level="info")
        else:
            logger.warning(f"❌ PAPER BUY FAILED: Insufficient balance. Required: ₹{total_cost:,.2f}, Available: ₹{self.virtual_balance:,.2f}")
            await self._send_notification(f"❌ Paper Trade Failed: Insufficient balance for {tradingsymbol}.", level="warning")

    async def _execute_paper_sell(self, tradingsymbol: str, quantity: int, entry_price: float,
                                 total_cost: float, stoploss: Optional[float], target: Optional[float],
                                 signal: Dict[str, Any]):
        """Execute paper SELL trade."""
        self.virtual_balance += (total_cost - (total_cost * 0.0003))  # Credit minus brokerage
        self.open_virtual_positions[tradingsymbol] = {
            "qty": -quantity,  # Negative for short position
            "entry_price": entry_price,
            "action": "SELL",
            "stoploss": stoploss,
            "target": target,
            "pnl_history": [],
            "current_pnl": 0.0,
            "last_ltp": entry_price,
            "entry_time": datetime.now(),
            "strategy_id": signal.get('strategy_id'),
            "confidence": signal.get('confidence_score'),
            "underlying": signal.get('underlying'),
            "strike_price": signal.get('strike_price'),
            "option_type": signal.get('option_type'),
            "expiry": signal.get('expiry')
        }
        self._log_paper_trade(tradingsymbol, "SELL", quantity, entry_price, "OPEN")
        logger.info(f"✅ PAPER SELL: {quantity} of {tradingsymbol} @ ₹{entry_price:.2f}. Balance: ₹{self.virtual_balance:,.2f}")
        await self._send_notification(f"📝 Paper Trade: SELL {quantity} of {tradingsymbol} @ ₹{entry_price:.2f}", level="info")

    def _log_paper_trade(self, tradingsymbol: str, action: str, quantity: int, price: float, status: str, realized_pnl: float = 0.0):
        """Log paper trade to virtual trades log."""
        trade_data = {
            "timestamp": datetime.now().isoformat(),
            "tradingsymbol": tradingsymbol,
            "action": action,
            "quantity": quantity,
            "entry_price": price,
            "status": status,
            "mode": "paper",
            "realized_pnl": realized_pnl
        }
        self.virtual_trades_log.append(trade_data)
        self.log_trade(trade_data)

    async def _evaluate_signal_risk(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate signal through risk management agent."""
        try:
            if self.risk_management_agent:
                return await self.risk_management_agent.evaluate_signal_for_risk(signal)
            else:
                # Basic risk evaluation if agent not available
                return self._basic_risk_evaluation(signal)
        except Exception as e:
            logger.error(f"❌ Error in risk evaluation: {e}")
            return {"approved": False, "reason": f"Risk evaluation error: {e}"}

    def _basic_risk_evaluation(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Basic risk evaluation when risk management agent is not available."""
        # Check basic parameters
        if not signal.get('underlying') or not signal.get('action'):
            return {"approved": False, "reason": "Missing required signal parameters"}

        # Check position limits
        max_positions = self.config.get('safety_checks', {}).get('max_positions_per_underlying', 3)
        underlying = signal.get('underlying')
        current_positions = sum(1 for pos in self.open_virtual_positions.values()
                              if pos.get('underlying') == underlying)

        if current_positions >= max_positions:
            return {"approved": False, "reason": f"Max positions limit reached for {underlying}"}

        # Check capital allocation
        max_capital_pct = self.config.get('safety_checks', {}).get('max_capital_per_trade_percent', 0.05)
        trade_value = signal.get('entry_price', 0) * signal.get('lot_size', 1) * \
                     self.config.get('market_settings', {}).get(underlying.lower(), {}).get('lot_size', 25)

        if trade_value > (self.virtual_balance * max_capital_pct):
            return {"approved": False, "reason": "Trade size exceeds maximum capital allocation"}

        return {"approved": True, "reason": "Basic risk checks passed"}

    async def _execute_real_trade(self, signal: Dict[str, Any], risk_evaluation: Dict[str, Any]):
        """Execute real trade through execution agent."""
        try:
            if not self.execution_agent:
                logger.error("❌ Execution agent not available for real trading")
                return

            # Log real trade attempt
            self.log_trade({
                "timestamp": datetime.now().isoformat(),
                "signal": dict(signal),
                "risk_evaluation": risk_evaluation,
                "mode": "real",
                "status": "attempting"
            })

            # Process signal through execution agent
            order_result = await self.execution_agent._process_signal(signal)

            if order_result:
                logger.info(f"✅ Real trade executed: {order_result}")
                self.log_trade({
                    "timestamp": datetime.now().isoformat(),
                    "order_result": order_result,
                    "mode": "real",
                    "status": "executed"
                })
                await self._send_notification(f"✅ Real Trade Executed: {signal.get('tradingsymbol', 'Unknown')}", level="info")
            else:
                logger.warning(f"❌ Real trade execution failed for: {signal.get('strategy_id')}")
                self.log_error("Real trade execution failed", {"signal": dict(signal)})

        except Exception as e:
            error_msg = f"Error executing real trade: {e}"
            logger.error(f"❌ {error_msg}")
            self.log_error(error_msg, {"signal": dict(signal), "risk_evaluation": risk_evaluation})

    async def _update_virtual_positions_pnl(self):
        """
        Enhanced P&L update for virtual positions with better price fetching and SL/TP monitoring.
        """
        if not self.open_virtual_positions:
            return

        logger.debug("📈 Updating virtual positions P&L...")

        # Get latest prices for all open positions
        latest_prices = await self._fetch_latest_option_prices()

        positions_to_close = []

        for tradingsymbol, pos in self.open_virtual_positions.items():
            try:
                # Get current price
                current_ltp = await self._get_current_option_price(pos, latest_prices)
                if not current_ltp:
                    continue

                # Calculate P&L
                entry_price = pos['entry_price']
                quantity = pos['qty']
                action = pos['action']

                if action == "BUY":
                    pnl = (current_ltp - entry_price) * quantity
                else:  # SELL
                    pnl = (entry_price - current_ltp) * abs(quantity)

                # Update position
                pos['current_pnl'] = pnl
                pos['last_ltp'] = current_ltp
                pos['pnl_history'].append({
                    "timestamp": datetime.now().isoformat(),
                    "ltp": current_ltp,
                    "pnl": pnl
                })

                # Check for SL/TP hits
                sl_tp_result = self._check_sl_tp_hit(pos, current_ltp)
                if sl_tp_result:
                    positions_to_close.append((tradingsymbol, sl_tp_result))

                # Log P&L update (less frequent to avoid spam)
                if len(pos['pnl_history']) % 10 == 0:  # Log every 10th update
                    logger.info(f"📊 {tradingsymbol}: P&L ₹{pnl:,.2f} (LTP: ₹{current_ltp:.2f})")

            except Exception as e:
                logger.error(f"❌ Error updating P&L for {tradingsymbol}: {e}")

        # Close positions that hit SL/TP
        for tradingsymbol, close_info in positions_to_close:
            await self._close_virtual_position(tradingsymbol, close_info)

    async def _fetch_latest_option_prices(self) -> Dict[str, float]:
        """Fetch latest option prices from market monitoring agent."""
        latest_prices = {}

        try:
            for underlying in self.market_monitoring_agent.config.get('underlying_symbols', ['NIFTY', 'BANKNIFTY']):
                market_data = self.market_monitoring_agent.market_data_cache.get('1min', {}).get(underlying, {})

                if 'option_chain' in market_data and not market_data['option_chain'].is_empty():
                    for row in market_data['option_chain'].iter_rows(named=True):
                        symbol = f"{underlying}_{row['strike_price']}{row['option_type']}_{row['expiry_date']}"
                        latest_prices[symbol] = row['close']

        except Exception as e:
            logger.error(f"❌ Error fetching option prices: {e}")

        return latest_prices

    async def _get_current_option_price(self, position: Dict[str, Any], latest_prices: Dict[str, float]) -> Optional[float]:
        """Get current option price for a position."""
        # Try to get from latest prices first
        tradingsymbol = f"{position['underlying']}_{position['strike_price']}{position['option_type']}_{position['expiry']}"

        if tradingsymbol in latest_prices:
            return latest_prices[tradingsymbol]

        # Fallback to simulation
        try:
            underlying_price = await self._get_underlying_price(position['underlying'])
            if underlying_price:
                return self._simulate_option_price(
                    underlying_price,
                    position['strike_price'],
                    position['option_type']
                )
        except Exception as e:
            logger.error(f"❌ Error getting current price for {tradingsymbol}: {e}")

        return None

    def _check_sl_tp_hit(self, position: Dict[str, Any], current_ltp: float) -> Optional[Dict[str, Any]]:
        """Check if position hit stop loss or take profit."""
        action = position['action']
        stoploss = position.get('stoploss')
        target = position.get('target')

        if action == "BUY":
            if stoploss and current_ltp <= stoploss:
                return {"type": "SL_HIT", "price": stoploss, "reason": "Stop Loss Hit"}
            elif target and current_ltp >= target:
                return {"type": "TP_HIT", "price": target, "reason": "Take Profit Hit"}
        else:  # SELL
            if stoploss and current_ltp >= stoploss:
                return {"type": "SL_HIT", "price": stoploss, "reason": "Stop Loss Hit"}
            elif target and current_ltp <= target:
                return {"type": "TP_HIT", "price": target, "reason": "Take Profit Hit"}

        return None

    async def _close_virtual_position(self, tradingsymbol: str, close_info: Dict[str, Any]):
        """Close a virtual position due to SL/TP hit."""
        if tradingsymbol not in self.open_virtual_positions:
            return

        pos = self.open_virtual_positions[tradingsymbol]
        exit_price = close_info['price']
        exit_type = close_info['type']

        # Calculate realized P&L
        entry_price = pos['entry_price']
        quantity = pos['qty']
        action = pos['action']

        if action == "BUY":
            realized_pnl = (exit_price - entry_price) * quantity
            self.virtual_balance += exit_price * quantity
        else:  # SELL
            realized_pnl = (entry_price - exit_price) * abs(quantity)
            self.virtual_balance -= exit_price * abs(quantity)

        # Update consecutive SL hits counter
        if exit_type == "SL_HIT":
            self.consecutive_sl_hits += 1
            emoji = "🛑"
            level = "warning"
        else:
            self.consecutive_sl_hits = 0
            emoji = "🎯"
            level = "info"

        # Log the exit
        self._log_paper_trade(tradingsymbol, f"EXIT_{exit_type}", abs(quantity), exit_price, exit_type, realized_pnl)

        # Remove from open positions
        del self.open_virtual_positions[tradingsymbol]

        logger.info(f"{emoji} VIRTUAL {exit_type}: {tradingsymbol} @ ₹{exit_price:.2f}. P&L: ₹{realized_pnl:,.2f}. Balance: ₹{self.virtual_balance:,.2f}")
        await self._send_notification(f"{emoji} Virtual {exit_type}: {tradingsymbol} @ ₹{exit_price:.2f}. P&L: ₹{realized_pnl:,.2f}", level=level)

    async def _paper_trading_pnl_update_loop(self):
        """
        Loop to periodically update P&L for virtual positions.
        """
        if self.mode != "paper":
            return # Only run in paper trading mode

        logger.info("📈 Starting Paper Trading P&L Update Loop...")
        while self.is_running:
            now = datetime.now()
            if self.start_time <= now <= self.end_time:
                await self._update_virtual_positions_pnl()
            else:
                logger.info(f"😴 Outside trading hours for paper P&L update. Current time: {now.strftime('%H:%M:%S')}")
            await asyncio.sleep(self.config.get("signal_check_interval_seconds", 5)) # Update as frequently as signals

    def _log_virtual_trade_exit(self, tradingsymbol: str, exit_type: str, exit_price: float, realized_pnl: float):
        """Logs a virtual trade exit."""
        for trade in self.virtual_trades_log:
            if trade['tradingsymbol'] == tradingsymbol and trade['status'] == 'OPEN':
                trade['status'] = exit_type
                trade['exit_price'] = exit_price
                trade['exit_time'] = datetime.now().isoformat()
                trade['realized_pnl'] = realized_pnl
                break
        else:
            logger.warning(f"Could not find open virtual trade for {tradingsymbol} to log exit.")

    async def _safety_and_failsafe_loop(self):
        """
        Enhanced safety monitoring with comprehensive fail-safes and risk controls.
        """
        logger.info("🚨 Starting Enhanced Safety and Fail-Safe Loop...")
        safety_check_count = 0
        last_performance_log = datetime.now()

        while self.is_running:
            try:
                safety_check_count += 1
                current_time = datetime.now()

                # Comprehensive safety checks
                safety_status = await self._perform_comprehensive_safety_checks()

                # Log safety status periodically
                if safety_check_count % 10 == 0:  # Every 10 minutes
                    self.log_performance({
                        "event": "safety_check",
                        "status": safety_status,
                        "timestamp": current_time.isoformat(),
                        "check_count": safety_check_count
                    })

                # Check if any critical issues detected
                if safety_status.get("critical_issues"):
                    await self._handle_critical_safety_issues(safety_status["critical_issues"])
                    break

                # Check for warning conditions
                if safety_status.get("warnings"):
                    await self._handle_safety_warnings(safety_status["warnings"])

                # Performance monitoring (every 5 minutes)
                if (current_time - last_performance_log).total_seconds() >= 300:
                    await self._log_performance_metrics()
                    last_performance_log = current_time

            except Exception as e:
                error_msg = f"Error in safety and fail-safe loop: {e}"
                logger.error(f"❌ {error_msg}")
                self.log_error(error_msg)

            await asyncio.sleep(60)  # Check safety every minute

    async def _perform_comprehensive_safety_checks(self) -> Dict[str, Any]:
        """Perform comprehensive safety checks and return status."""
        safety_status = {
            "critical_issues": [],
            "warnings": [],
            "metrics": {},
            "timestamp": datetime.now().isoformat()
        }

        # 1. API Connection and Data Feed Checks
        await self._check_api_connectivity(safety_status)

        # 2. Capital and Drawdown Monitoring
        await self._check_capital_drawdown(safety_status)

        # 3. Position and Risk Monitoring
        await self._check_position_risks(safety_status)

        # 4. Market Condition Monitoring
        await self._check_market_conditions(safety_status)

        # 5. System Health Monitoring
        await self._check_system_health(safety_status)

        return safety_status

    async def _check_api_connectivity(self, safety_status: Dict[str, Any]):
        """Check API connectivity and data feed health."""
        try:
            if hasattr(self.market_monitoring_agent, 'last_update'):
                last_update = self.market_monitoring_agent.last_update.get('1min')
                if last_update:
                    time_since_update = (datetime.now() - last_update).total_seconds()
                    threshold = self.config['safety_checks']['api_disconnection_threshold_minutes'] * 60

                    safety_status["metrics"]["time_since_last_update"] = time_since_update

                    if time_since_update > threshold:
                        safety_status["critical_issues"].append({
                            "type": "api_disconnection",
                            "message": f"No market data updates for {time_since_update/60:.1f} minutes",
                            "severity": "critical"
                        })
                    elif time_since_update > threshold * 0.7:  # Warning at 70% of threshold
                        safety_status["warnings"].append({
                            "type": "api_delay",
                            "message": f"Market data delay: {time_since_update/60:.1f} minutes",
                            "severity": "warning"
                        })
        except Exception as e:
            logger.error(f"❌ Error checking API connectivity: {e}")

    async def _check_capital_drawdown(self, safety_status: Dict[str, Any]):
        """Check capital drawdown and position sizing."""
        try:
            if self.mode == "paper":
                initial_capital = self.config.get("capital", 100000.0)
                current_drawdown_pct = (initial_capital - self.virtual_balance) / initial_capital
                safety_status["metrics"]["drawdown_percent"] = current_drawdown_pct
                safety_status["metrics"]["current_balance"] = self.virtual_balance

                max_drawdown = self.config['safety_checks']['max_drawdown_percent']
                if current_drawdown_pct > max_drawdown:
                    safety_status["critical_issues"].append({
                        "type": "max_drawdown_exceeded",
                        "message": f"Drawdown {current_drawdown_pct:.2%} exceeds limit {max_drawdown:.2%}",
                        "severity": "critical"
                    })
                elif current_drawdown_pct > max_drawdown * 0.8:  # Warning at 80% of limit
                    safety_status["warnings"].append({
                        "type": "high_drawdown",
                        "message": f"High drawdown: {current_drawdown_pct:.2%}",
                        "severity": "warning"
                    })
            else:
                # For real trading, get drawdown from performance agent
                if self.performance_analysis_agent:
                    try:
                        # Try to calculate drawdown from available data
                        current_drawdown_pct = await self._calculate_real_trading_drawdown()
                        safety_status["metrics"]["drawdown_percent"] = current_drawdown_pct

                        max_drawdown = self.config['safety_checks']['max_drawdown_percent']
                        if current_drawdown_pct > max_drawdown:
                            safety_status["critical_issues"].append({
                                "type": "max_drawdown_exceeded",
                                "message": f"Real trading drawdown {current_drawdown_pct:.2%} exceeds limit",
                                "severity": "critical"
                            })
                    except Exception as e:
                        logger.error(f"❌ Error calculating real trading drawdown: {e}")
                        safety_status["metrics"]["drawdown_percent"] = 0.0
        except Exception as e:
            logger.error(f"❌ Error checking capital drawdown: {e}")

    async def _check_position_risks(self, safety_status: Dict[str, Any]):
        """Check position-related risks."""
        try:
            # Check consecutive SL hits
            consecutive_sl_hits = self.consecutive_sl_hits
            if self.mode == "real" and self.performance_analysis_agent:
                try:
                    # For real trading, use internal counter for now
                    # In a full implementation, this would query the execution agent
                    consecutive_sl_hits = self.consecutive_sl_hits
                except Exception as e:
                    logger.error(f"❌ Error getting consecutive SL hits: {e}")
                    consecutive_sl_hits = self.consecutive_sl_hits

            safety_status["metrics"]["consecutive_sl_hits"] = consecutive_sl_hits

            sl_limit = self.config['safety_checks']['consecutive_sl_hits_limit']
            if consecutive_sl_hits >= sl_limit:
                safety_status["critical_issues"].append({
                    "type": "consecutive_sl_hits",
                    "message": f"Consecutive SL hits: {consecutive_sl_hits}",
                    "severity": "critical"
                })
            elif consecutive_sl_hits >= sl_limit - 1:  # Warning one before limit
                safety_status["warnings"].append({
                    "type": "high_sl_hits",
                    "message": f"High consecutive SL hits: {consecutive_sl_hits}",
                    "severity": "warning"
                })

            # Check position concentration
            total_positions = len(self.open_virtual_positions)
            safety_status["metrics"]["total_open_positions"] = total_positions

            if total_positions > 10:  # Arbitrary high number
                safety_status["warnings"].append({
                    "type": "high_position_count",
                    "message": f"High number of open positions: {total_positions}",
                    "severity": "warning"
                })

        except Exception as e:
            logger.error(f"❌ Error checking position risks: {e}")

    async def _check_market_conditions(self, safety_status: Dict[str, Any]):
        """Check market conditions for extreme events."""
        try:
            if hasattr(self.market_monitoring_agent, 'market_regime_state'):
                regime_state = self.market_monitoring_agent.market_regime_state

                for timeframe, underlyings in regime_state.items():
                    for underlying, regime_data in underlyings.items():
                        if regime_data.get('volatility_regime') == 'high_iv':
                            iv_value = regime_data.get('iv_value', 0)
                            threshold = self.config['safety_checks']['vix_spike_threshold']

                            if iv_value > threshold:
                                safety_status["critical_issues"].append({
                                    "type": "extreme_volatility",
                                    "message": f"Extreme volatility in {underlying}: IV {iv_value:.2f}",
                                    "severity": "critical"
                                })
                            elif iv_value > threshold * 0.8:
                                safety_status["warnings"].append({
                                    "type": "high_volatility",
                                    "message": f"High volatility in {underlying}: IV {iv_value:.2f}",
                                    "severity": "warning"
                                })
        except Exception as e:
            logger.error(f"❌ Error checking market conditions: {e}")

    async def _check_system_health(self, safety_status: Dict[str, Any]):
        """Check system health metrics."""
        try:
            # Check memory usage, CPU, etc. (basic implementation)
            import psutil

            memory_percent = psutil.virtual_memory().percent
            cpu_percent = psutil.cpu_percent()

            safety_status["metrics"]["memory_usage_percent"] = memory_percent
            safety_status["metrics"]["cpu_usage_percent"] = cpu_percent

            if memory_percent > 90:
                safety_status["warnings"].append({
                    "type": "high_memory_usage",
                    "message": f"High memory usage: {memory_percent:.1f}%",
                    "severity": "warning"
                })

            if cpu_percent > 90:
                safety_status["warnings"].append({
                    "type": "high_cpu_usage",
                    "message": f"High CPU usage: {cpu_percent:.1f}%",
                    "severity": "warning"
                })

        except ImportError:
            # psutil not available
            pass
        except Exception as e:
            logger.error(f"❌ Error checking system health: {e}")

    async def _calculate_real_trading_drawdown(self) -> float:
        """Calculate drawdown for real trading mode."""
        try:
            # For real trading, this would query the execution agent for account balance
            # For now, return 0 as a placeholder
            if self.execution_agent and hasattr(self.execution_agent, 'get_account_balance'):
                # This would be the real implementation
                current_balance = await self.execution_agent.get_account_balance()
                initial_balance = self.config.get('capital', 100000.0)
                if current_balance < initial_balance:
                    return (initial_balance - current_balance) / initial_balance
            return 0.0
        except Exception as e:
            logger.error(f"❌ Error calculating real trading drawdown: {e}")
            return 0.0

    async def _handle_critical_safety_issues(self, critical_issues: List[Dict[str, Any]]):
        """Handle critical safety issues by stopping trading."""
        for issue in critical_issues:
            logger.critical(f"🛑 CRITICAL SAFETY ISSUE: {issue['message']}")
            await self._send_notification(f"🛑 CRITICAL: {issue['message']} - Trading aborted.", level="critical")

            # Log the critical issue
            self.log_risk_event({
                "event": "critical_safety_issue",
                "issue": issue,
                "timestamp": datetime.now().isoformat(),
                "action": "trading_stopped"
            })

        # Stop trading
        await self.stop_trading()

    async def _handle_safety_warnings(self, warnings: List[Dict[str, Any]]):
        """Handle safety warnings."""
        for warning in warnings:
            logger.warning(f"⚠️ SAFETY WARNING: {warning['message']}")
            await self._send_notification(f"⚠️ WARNING: {warning['message']}", level="warning")

            # Log the warning
            self.log_risk_event({
                "event": "safety_warning",
                "warning": warning,
                "timestamp": datetime.now().isoformat()
            })

    async def _log_performance_metrics(self):
        """Log current performance metrics."""
        try:
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "mode": self.mode,
                "virtual_balance": self.virtual_balance if self.mode == "paper" else "N/A",
                "open_positions": len(self.open_virtual_positions),
                "total_trades": len(self.virtual_trades_log),
                "consecutive_sl_hits": self.consecutive_sl_hits
            }

            # Calculate current P&L
            if self.mode == "paper":
                initial_capital = self.config.get("capital", 100000.0)
                current_pnl = self.virtual_balance - initial_capital
                metrics["current_pnl"] = current_pnl
                metrics["roi_percent"] = (current_pnl / initial_capital) * 100

            self.log_performance(metrics)

        except Exception as e:
            logger.error(f"❌ Error logging performance metrics: {e}")

    async def _performance_monitoring_loop(self):
        """
        Real-time performance monitoring and metrics calculation.
        """
        logger.info("📊 Starting Performance Monitoring Loop...")

        if not self.config.get("performance", {}).get("real_time_metrics", True):
            logger.info("📊 Real-time performance monitoring disabled in config.")
            return

        update_interval = self.config.get("performance", {}).get("update_interval_seconds", 60)
        last_metrics_calculation = datetime.now()

        while self.is_running:
            try:
                now = datetime.now()

                # Calculate and log performance metrics
                if (now - last_metrics_calculation).total_seconds() >= update_interval:
                    await self._calculate_and_log_performance_metrics()
                    last_metrics_calculation = now

                # Generate performance alerts if needed
                await self._check_performance_alerts()

            except Exception as e:
                logger.error(f"❌ Error in performance monitoring loop: {e}")

            await asyncio.sleep(30)  # Check every 30 seconds

    async def _calculate_and_log_performance_metrics(self):
        """Calculate comprehensive performance metrics."""
        try:
            metrics = await self._calculate_comprehensive_metrics()

            # Log to performance logger
            self.log_performance(metrics)

            # Log key metrics to main logger (less frequently)
            if metrics.get("total_trades", 0) > 0:
                logger.info(f"📊 Performance Update: ROI: {metrics.get('roi_percent', 0):.2f}%, "
                          f"Win Rate: {metrics.get('win_rate', 0):.2%}, "
                          f"Trades: {metrics.get('total_trades', 0)}")

        except Exception as e:
            logger.error(f"❌ Error calculating performance metrics: {e}")

    async def _calculate_comprehensive_metrics(self) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics."""
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "mode": self.mode,
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "win_rate": 0.0,
            "total_pnl": 0.0,
            "roi_percent": 0.0,
            "avg_win": 0.0,
            "avg_loss": 0.0,
            "profit_factor": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "open_positions": len(self.open_virtual_positions),
            "consecutive_sl_hits": self.consecutive_sl_hits
        }

        if self.mode == "paper":
            # Calculate metrics from virtual trades
            trades = self.virtual_trades_log
            closed_trades = [t for t in trades if t.get('status') not in ['OPEN']]

            if closed_trades:
                metrics["total_trades"] = len(closed_trades)

                # Calculate P&L metrics
                pnls = [t.get('realized_pnl', 0) for t in closed_trades]
                winning_pnls = [p for p in pnls if p > 0]
                losing_pnls = [p for p in pnls if p < 0]

                metrics["winning_trades"] = len(winning_pnls)
                metrics["losing_trades"] = len(losing_pnls)
                metrics["win_rate"] = len(winning_pnls) / len(closed_trades) if closed_trades else 0
                metrics["total_pnl"] = sum(pnls)

                # Calculate ROI
                initial_capital = self.config.get("capital", 100000.0)
                metrics["roi_percent"] = (metrics["total_pnl"] / initial_capital) * 100

                # Calculate average win/loss
                metrics["avg_win"] = sum(winning_pnls) / len(winning_pnls) if winning_pnls else 0
                metrics["avg_loss"] = sum(losing_pnls) / len(losing_pnls) if losing_pnls else 0

                # Calculate profit factor
                total_wins = sum(winning_pnls)
                total_losses = abs(sum(losing_pnls))
                metrics["profit_factor"] = total_wins / total_losses if total_losses > 0 else float('inf')

                # Calculate max drawdown
                metrics["max_drawdown"] = await self._calculate_max_drawdown()

                # Calculate Sharpe ratio (simplified)
                if len(pnls) > 1:
                    import statistics
                    avg_return = statistics.mean(pnls)
                    std_return = statistics.stdev(pnls)
                    risk_free_rate = self.config.get("performance", {}).get("risk_free_rate", 0.05) / 252  # Daily
                    metrics["sharpe_ratio"] = (avg_return - risk_free_rate) / std_return if std_return > 0 else 0

        else:
            # For real trading, get metrics from performance analysis agent
            if self.performance_analysis_agent:
                try:
                    # Use basic metrics for now since the agent doesn't have the expected method
                    logger.info("Real trading performance metrics not fully implemented yet")
                    # In a full implementation, this would query the execution agent for real trade data
                except Exception as e:
                    logger.error(f"❌ Error getting metrics from performance agent: {e}")

        return metrics

    async def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown from trade history."""
        try:
            if not self.virtual_trades_log:
                return 0.0

            # Calculate running balance
            initial_capital = self.config.get("capital", 100000.0)
            running_balance = initial_capital
            peak_balance = initial_capital
            max_drawdown = 0.0

            for trade in self.virtual_trades_log:
                if trade.get('status') not in ['OPEN']:
                    running_balance += trade.get('realized_pnl', 0)

                    if running_balance > peak_balance:
                        peak_balance = running_balance

                    current_drawdown = (peak_balance - running_balance) / peak_balance
                    max_drawdown = max(max_drawdown, current_drawdown)

            return max_drawdown

        except Exception as e:
            logger.error(f"❌ Error calculating max drawdown: {e}")
            return 0.0

    async def _check_performance_alerts(self):
        """Check for performance-based alerts."""
        try:
            # Check if performance has degraded significantly
            if len(self.virtual_trades_log) >= 10:  # Only after sufficient trades
                recent_trades = self.virtual_trades_log[-10:]  # Last 10 trades
                recent_pnl = sum(t.get('realized_pnl', 0) for t in recent_trades if t.get('status') not in ['OPEN'])

                if recent_pnl < -5000:  # Alert if last 10 trades lost more than ₹5000
                    await self._send_notification(
                        f"📉 Performance Alert: Recent 10 trades P&L: ₹{recent_pnl:,.2f}",
                        level="warning"
                    )

                    # Log performance alert
                    self.log_risk_event({
                        "event": "performance_degradation",
                        "recent_pnl": recent_pnl,
                        "trades_count": len(recent_trades),
                        "timestamp": datetime.now().isoformat()
                    })

        except Exception as e:
            logger.error(f"❌ Error checking performance alerts: {e}")

    async def _position_management_loop(self):
        """
        Monitors live positions, P&L, and handles auto-square off.
        """
        logger.info("💰 Starting Position Management Loop...")

        while self.is_running:
            try:
                # Monitor positions and handle auto-square off
                if self.mode == "paper":
                    await self._monitor_virtual_positions()
                else:
                    await self._monitor_real_positions()

                # Check for end-of-day square off
                current_time = datetime.now().time()
                if current_time >= self.eod_square_off_time.time():
                    logger.info("🕐 End-of-day time reached. Squaring off all positions...")
                    await self._square_off_all_positions()
                    break

            except Exception as e:
                logger.error(f"❌ Error in position management loop: {e}")

            await asyncio.sleep(30)  # Check every 30 seconds

    async def _monitor_virtual_positions(self):
        """Monitor virtual positions for paper trading."""
        if not self.open_virtual_positions:
            return

        # Update P&L for all positions
        await self._update_virtual_positions_pnl()

        # Log position summary periodically
        total_pnl = sum(pos.get('current_pnl', 0) for pos in self.open_virtual_positions.values())
        if len(self.open_virtual_positions) > 0:
            logger.debug(f"📊 Virtual Positions: {len(self.open_virtual_positions)} open, Total P&L: ₹{total_pnl:,.2f}")

    async def _monitor_real_positions(self):
        """Monitor real positions for live trading."""
        if self.execution_agent:
            # Let the execution agent handle real position monitoring
            await self.execution_agent.monitor_positions()

    async def _square_off_all_positions(self):
        """Square off all open positions at end of day."""
        if self.mode == "paper":
            await self._square_off_virtual_positions()
        else:
            await self._square_off_real_positions()

    async def _square_off_virtual_positions(self):
        """Square off all virtual positions."""
        logger.info("⏰ Squaring off all open virtual positions...")

        for tradingsymbol in list(self.open_virtual_positions.keys()):
            pos = self.open_virtual_positions[tradingsymbol]

            # Simulate market close price (use last LTP)
            exit_price = pos.get('last_ltp', pos.get('entry_price', 0))

            # Calculate final P&L
            entry_price = pos['entry_price']
            quantity = pos['qty']
            action = pos['action']

            if action == "BUY":
                realized_pnl = (exit_price - entry_price) * quantity
                self.virtual_balance += exit_price * quantity
            else:  # SELL
                realized_pnl = (entry_price - exit_price) * abs(quantity)
                self.virtual_balance -= exit_price * abs(quantity)

            # Log the square off
            self._log_paper_trade(tradingsymbol, "SQUARE_OFF", abs(quantity), exit_price, "CLOSED", realized_pnl)

            logger.info(f"⏰ Squared off {tradingsymbol}: P&L ₹{realized_pnl:,.2f}")

        # Clear all positions
        self.open_virtual_positions.clear()
        logger.info("✅ All virtual positions squared off.")

    async def _square_off_real_positions(self):
        """Square off all real positions."""
        if self.execution_agent:
            await self.execution_agent.square_off_all_positions()

    async def _daily_summary_loop(self):
        """
        Generates daily summary at day-end or on exit.
        """
        logger.info("📝 Starting Daily Summary Loop...")
        summary_generated_today = False
        while self.is_running:
            now = datetime.now()
            # Check if it's time for EOD summary and not already generated today
            if now.time() >= self.eod_square_off_time.time() and not summary_generated_today:
                logger.info("Generating daily summary...")
                await self._generate_daily_summary()
                summary_generated_today = True # Mark as generated for today
            elif now.time() < self.eod_square_off_time.time() and summary_generated_today:
                # Reset for next day
                summary_generated_today = False
            
            await asyncio.sleep(300) # Check every 5 minutes

    async def _generate_daily_summary(self):
        """
        Collects data from various agents and generates a comprehensive daily summary.
        """
        logger.info("📊 Generating comprehensive daily summary...")
        summary_data = {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "mode": self.mode,
            "total_trades": 0,
            "realized_pnl": 0.0,
            "unrealized_pnl": 0.0,
            "capital_left": self.virtual_balance if self.mode == "paper" else "N/A (real money)",
            "win_rate": 0.0,
            "best_strategy": "N/A",
            "worst_strategy": "N/A",
            "strategy_wise_metrics": {},
            "llm_explanation": ""
        }

        # Get trade data from Execution Agent (or its logs)
        # For paper trading, use self.virtual_trades_log
        if self.mode == "paper":
            # Ensure all open virtual positions are squared off for final P&L calculation
            await self._square_off_all_virtual_positions()
            summary_data["total_trades"] = len(self.virtual_trades_log)
            # Calculate realized P&L from virtual trades
            summary_data["realized_pnl"] = sum(trade.get('realized_pnl', 0) for trade in self.virtual_trades_log)
            # Calculate win rate (simplified)
            winning_trades = sum(1 for trade in self.virtual_trades_log if trade.get('realized_pnl', 0) > 0)
            if summary_data["total_trades"] > 0:
                summary_data["win_rate"] = winning_trades / summary_data["total_trades"]
            
            # Calculate ROI for paper trading
            initial_capital = self.config.get("capital", 100000.0)
            summary_data["roi"] = (summary_data["realized_pnl"] / initial_capital) * 100 if initial_capital > 0 else 0.0
        else:
            # For real trading, query Execution Agent for realized P&L and trades
            # This would involve fetching historical trades from SmartAPI or local logs
            # For now, placeholder
            logger.warning("Real trading P&L and trade metrics not fully implemented for daily summary.")
            # summary_data["total_trades"] = await self.execution_agent.get_daily_total_trades()
            # summary_data["realized_pnl"] = await self.execution_agent.get_daily_realized_pnl()
            # summary_data["roi"] = await self.performance_analysis_agent.get_daily_roi()


        # Get performance metrics from Performance Analysis Agent
        try:
            # Use available methods from the performance analysis agent
            if hasattr(self.performance_analysis_agent, '_generate_trade_session_summary'):
                # Create a simple DataFrame from virtual trades for analysis
                if self.virtual_trades_log:
                    trades_data = []
                    for trade in self.virtual_trades_log:
                        if trade.get('status') not in ['OPEN']:
                            trades_data.append({
                                'entry_time': trade.get('timestamp', datetime.now().isoformat()),
                                'absolute_pnl': trade.get('realized_pnl', 0),
                                'pnl_category': 'Profit' if trade.get('realized_pnl', 0) > 0 else 'Loss',
                                'roi_percent': (trade.get('realized_pnl', 0) / self.config.get('capital', 100000)) * 100,
                                'strategy_id': trade.get('strategy_id', 'unknown')
                            })

                    if trades_data:
                        trades_df = pl.DataFrame(trades_data)
                        performance_metrics = await self.performance_analysis_agent._generate_trade_session_summary(trades_df)
                        summary_data.update(performance_metrics)
                else:
                    logger.info("No completed trades for performance analysis")
            else:
                logger.warning("Performance analysis agent method not available")
        except Exception as e:
            logger.error(f"❌ Error getting performance metrics: {e}")
            # Use basic metrics calculated earlier

        # Get market summary from Market Monitoring Agent
        try:
            # Create a simple market summary since the agent's method is complex
            market_summary = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": "Market monitoring active",
                "regime": "Normal trading conditions",
                "alerts": "No critical alerts"
            }

            # Try to get more detailed summary if available
            if hasattr(self.market_monitoring_agent, 'market_regime_state'):
                regime_state = getattr(self.market_monitoring_agent, 'market_regime_state', {})
                if regime_state:
                    market_summary["regime_details"] = str(regime_state)

            summary_data["market_summary"] = f"Market Status: {market_summary['status']} at {market_summary['timestamp']}"

        except Exception as e:
            logger.error(f"❌ Error getting market summary: {e}")
            summary_data["market_summary"] = "Market summary unavailable"

        # Get LLM explanation (if enabled)
        if self.config.get("enable_llm_summary"):
            llm_question = (
                f"Summarize today's trading performance and market conditions. "
                f"Total trades: {summary_data['total_trades']}, "
                f"Realized P&L: {summary_data['realized_pnl']:.2f}, "
                f"Win Rate: {summary_data['win_rate']:.2%}. "
                f"ROI: {summary_data.get('roi', 0.0):.2f}%. "
                f"Market conditions: {summary_data.get('market_summary', 'N/A')}"
            )
            llm_response = await self.llm_interface_agent._interactive_mode_feedback(llm_question)
            summary_data["llm_explanation"] = llm_response

        # Save summary to exports/YYYY-MM-DD-summary.json
        exports_path = Path("exports")
        exports_path.mkdir(parents=True, exist_ok=True)
        summary_filename = f"{datetime.now().strftime('%Y-%m-%d')}-summary.json"
        summary_filepath = exports_path / "reports" / summary_filename # Ensure 'reports' subdirectory
        summary_filepath.parent.mkdir(parents=True, exist_ok=True) # Create 'reports' if it doesn't exist
        
        async with aiofiles.open(summary_filepath, mode="w") as f:
            await f.write(json.dumps(summary_data, indent=2))
        logger.info(f"📝 Daily summary saved to {summary_filepath}")
        self._send_notification(f"📝 Daily Summary Generated for {summary_data['date']}. Realized P&L: ₹{summary_data['realized_pnl']:.2f}", level="info")

    async def _square_off_all_virtual_positions(self):
        """
        Squares off all open virtual positions at EOD or on exit.
        """
        if self.mode != "paper":
            return

        logger.info("⏰ Squaring off all open virtual positions...")
        positions_to_square_off = list(self.open_virtual_positions.keys()) # Get keys to avoid modifying dict during iteration
        for tradingsymbol in positions_to_square_off:
            pos = self.open_virtual_positions.get(tradingsymbol)
            if pos:
                # Simulate closing at current LTP
                current_ltp = self.market_monitoring_agent.market_data_cache.get('1min', {}).get(pos['underlying'], {}).get('option_chain', pl.DataFrame()).filter(
                    (pl.col('strike_price') == pos['strike_price']) &
                    (pl.col('option_type') == pos['option_type']) &
                    (pl.col('expiry_date') == pos['expiry'])
                ).select('close').head(1)
                
                if not current_ltp.is_empty():
                    exit_price = current_ltp['close'].item()
                else:
                    exit_price = pos['last_ltp'] # Fallback to last known LTP
                    logger.warning(f"⚠️ Could not get live LTP for {tradingsymbol} for EOD square-off. Using last known LTP: ₹{exit_price:.2f}")

                realized_pnl = 0.0
                if pos['action'] == "BUY":
                    realized_pnl = (exit_price - pos['entry_price']) * pos['qty']
                    self.virtual_balance += (pos['entry_price'] + realized_pnl / pos['qty']) * pos['qty']
                elif pos['action'] == "SELL":
                    realized_pnl = (pos['entry_price'] - exit_price) * abs(pos['qty'])
                    self.virtual_balance -= (exit_price - realized_pnl / abs(pos['qty'])) * abs(pos['qty'])
                
                self._log_virtual_trade_exit(tradingsymbol, "EOD_SQUARE_OFF", exit_price, realized_pnl)
                del self.open_virtual_positions[tradingsymbol]
                logger.info(f"✅ VIRTUAL EOD SQUARE-OFF for {tradingsymbol} at ₹{exit_price:.2f}. Realized P&L: ₹{realized_pnl:,.2f}. Balance: ₹{self.virtual_balance:,.2f}")
                self._send_notification(f"⏰ Virtual EOD Square-Off: {tradingsymbol} exited at ₹{exit_price:.2f}. P&L: ₹{realized_pnl:,.2f}", level="info")
        logger.info("✅ All virtual positions squared off.")

    async def _llm_interface_loop(self):
        """
        Enhanced LLM interface loop with interactive features and market insights.
        """
        logger.info("🧠 Starting Enhanced LLM Interface Loop...")

        if not self.config.get("enable_llm_summary", False):
            logger.info("🧠 LLM interface disabled in config. Skipping LLM loop.")
            return

        llm_query_interval = self.config.get("llm_query_interval", 900)  # 15 minutes default
        last_market_insight = datetime.now()

        while self.is_running:
            try:
                now = datetime.now()

                # Generate periodic market insights during trading hours
                if (self.start_time <= now <= self.end_time and
                    (now - last_market_insight).total_seconds() >= llm_query_interval):

                    await self._generate_llm_market_insight()
                    last_market_insight = now

                # Process any pending LLM queries (placeholder for future implementation)
                await self._process_pending_llm_queries()

                # Generate trading recommendations based on current state
                if len(self.open_virtual_positions) > 0:
                    await self._generate_position_analysis()

            except Exception as e:
                logger.error(f"❌ Error in LLM interface loop: {e}")

            await asyncio.sleep(60)  # Check every minute

    async def _generate_llm_market_insight(self):
        """Generate market insights using LLM."""
        try:
            # Gather current market context
            market_context = await self._gather_market_context()

            # Ask LLM for market analysis
            prompt = self._create_market_analysis_prompt(market_context)
            insight = await self.llm_interface_agent._interpret_market_conditions(prompt)

            logger.info(f"🧠 LLM Market Insight: {insight}")

            # Store insight for later use
            self._store_llm_insight(insight, "market_analysis")

        except Exception as e:
            logger.error(f"❌ Error generating LLM market insight: {e}")

    async def _generate_position_analysis(self):
        """Generate LLM analysis of current positions."""
        try:
            if not self.open_virtual_positions:
                return

            # Create position summary
            position_summary = self._create_position_summary()

            # Ask LLM for position analysis
            prompt = f"Analyze current trading positions and provide recommendations:\n{position_summary}"
            analysis = await self.llm_interface_agent._interactive_mode_feedback(prompt)

            logger.info(f"🧠 LLM Position Analysis: {analysis}")

        except Exception as e:
            logger.error(f"❌ Error generating position analysis: {e}")

    async def _process_pending_llm_queries(self):
        """Process any pending LLM queries from external sources."""
        # Placeholder for future implementation
        # This could read from a queue, file, or API endpoint
        pass

    async def _gather_market_context(self) -> Dict[str, Any]:
        """Gather current market context for LLM analysis."""
        context = {
            "timestamp": datetime.now().isoformat(),
            "trading_mode": self.mode,
            "virtual_balance": self.virtual_balance if self.mode == "paper" else "N/A",
            "open_positions": len(self.open_virtual_positions),
            "total_trades_today": len(self.virtual_trades_log),
            "consecutive_sl_hits": self.consecutive_sl_hits
        }

        # Add market regime information if available
        try:
            if hasattr(self.market_monitoring_agent, 'market_regime_state'):
                context["market_regime"] = self.market_monitoring_agent.market_regime_state
        except Exception as e:
            logger.debug(f"Could not get market regime: {e}")

        return context

    def _create_market_analysis_prompt(self, context: Dict[str, Any]) -> str:
        """Create a prompt for market analysis."""
        return f"""
        Analyze the current market conditions and trading session:

        Current Context:
        - Time: {context['timestamp']}
        - Trading Mode: {context['trading_mode']}
        - Open Positions: {context['open_positions']}
        - Total Trades Today: {context['total_trades_today']}
        - Consecutive SL Hits: {context['consecutive_sl_hits']}

        Please provide:
        1. Market sentiment analysis
        2. Risk assessment
        3. Trading recommendations
        4. Any alerts or warnings

        Keep the response concise and actionable.
        """

    def _create_position_summary(self) -> str:
        """Create a summary of current positions for LLM analysis."""
        if not self.open_virtual_positions:
            return "No open positions"

        summary_lines = ["Current Open Positions:"]

        for symbol, pos in self.open_virtual_positions.items():
            pnl_status = "📈" if pos['current_pnl'] >= 0 else "📉"
            summary_lines.append(
                f"- {symbol}: {pos['action']} {pos['qty']} @ ₹{pos['entry_price']:.2f} "
                f"(Current P&L: {pnl_status} ₹{pos['current_pnl']:.2f})"
            )

        return "\n".join(summary_lines)

    def _store_llm_insight(self, insight: str, insight_type: str):
        """Store LLM insights for later reference."""
        # Create insights directory if it doesn't exist
        insights_dir = Path("data/llm_insights")
        insights_dir.mkdir(parents=True, exist_ok=True)

        # Store insight with timestamp
        insight_data = {
            "timestamp": datetime.now().isoformat(),
            "type": insight_type,
            "insight": insight,
            "trading_mode": self.mode
        }

        # Save to daily insights file
        today = datetime.now().strftime("%Y-%m-%d")
        insights_file = insights_dir / f"{today}_insights.json"

        try:
            # Load existing insights or create new list
            if insights_file.exists():
                with open(insights_file, 'r') as f:
                    insights = json.load(f)
            else:
                insights = []

            insights.append(insight_data)

            # Save updated insights
            with open(insights_file, 'w') as f:
                json.dump(insights, f, indent=2)

        except Exception as e:
            logger.error(f"❌ Error storing LLM insight: {e}")

    async def _send_notification(self, message: str, level: str = "info"):
        """
        Enhanced notification system with LLM integration.
        """
        # Log notification
        if level == "info":
            logger.info(f"🔔 NOTIFICATION: {message}")
        elif level == "warning":
            logger.warning(f"🔔 NOTIFICATION: {message}")
        elif level == "critical":
            logger.critical(f"🔔 NOTIFICATION: {message}")

        # Send to LLM interface if enabled
        if self.config.get("enable_llm_summary") and self.llm_interface_agent:
            try:
                # Format message for LLM
                llm_message = f"System Alert ({level.upper()}): {message}"

                # Use the correct method name from the LLM interface agent
                if hasattr(self.llm_interface_agent, '_process_system_alert'):
                    await self.llm_interface_agent._process_system_alert(llm_message)
                else:
                    # Fallback to a general method
                    logger.debug(f"🧠 LLM Alert: {llm_message}")

            except Exception as e:
                logger.error(f"❌ Error sending notification to LLM: {e}")

    async def ask_llm(self, question: str) -> str:
        """
        Public method to ask LLM questions.
        """
        if not self.llm_interface_agent:
            return "LLM interface not available"

        try:
            return await self.llm_interface_agent._interactive_mode_feedback(question)
        except Exception as e:
            logger.error(f"❌ Error asking LLM: {e}")
            return f"Error: {e}"

    async def explain_trade_decision(self, signal: Dict[str, Any]) -> str:
        """
        Get LLM explanation for a trade decision.
        """
        if not self.llm_interface_agent:
            return "LLM interface not available"

        try:
            return await self.llm_interface_agent._debug_trade_signal(json.dumps(signal))
        except Exception as e:
            logger.error(f"❌ Error explaining trade decision: {e}")
            return f"Error: {e}"

    async def stop_trading(self):
        """
        Stops all trading activities and cleans up resources.
        """
        logger.info("🛑 Stopping Live Trading System...")
        self.is_running = False

        # Ensure all virtual positions are squared off before stopping
        if self.mode == "paper":
            await self._square_off_virtual_positions()

        # Stop all agents (only if they exist)
        cleanup_tasks = []

        if self.market_monitoring_agent:
            cleanup_tasks.append(self.market_monitoring_agent.cleanup())
        if self.signal_generation_agent:
            cleanup_tasks.append(self.signal_generation_agent.cleanup())
        if self.risk_management_agent:
            cleanup_tasks.append(self.risk_management_agent.cleanup())
        if self.execution_agent:
            cleanup_tasks.append(self.execution_agent.cleanup())
        if self.performance_analysis_agent:
            cleanup_tasks.append(self.performance_analysis_agent.cleanup())
        if self.ai_training_agent:
            cleanup_tasks.append(self.ai_training_agent.cleanup())
        if self.llm_interface_agent:
            cleanup_tasks.append(self.llm_interface_agent.cleanup())

        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        logger.info("✅ All agents cleaned up.")
        logger.info("🛑 Live Trading System stopped.")

async def main():
    parser = argparse.ArgumentParser(description="Run the automated options trading system.")
    parser.add_argument("--paper_trading", action="store_true", help="Run in paper trading mode (virtual money).")
    parser.add_argument("--real", action="store_true", help="Run in real money trading mode (requires SmartAPI credentials).")
    
    args = parser.parse_args()

    if args.paper_trading and args.real:
        logger.error("❌ Cannot run in both paper trading and real money mode simultaneously. Choose one.")
        return
    
    if not args.paper_trading and not args.real:
        logger.warning("⚠️ No mode specified. Defaulting to paper trading mode.")
        mode = "paper"
    elif args.paper_trading:
        mode = "paper"
    else: # args.real is True
        mode = "real"

    system = LiveTradingSystem(mode=mode)
    try:
        initialized = await system.initialize()
        if initialized:
            await system.start_trading()
        else:
            logger.critical("System initialization failed. Exiting.")
    except KeyboardInterrupt:
        logger.info("User interrupted the trading system. Initiating graceful shutdown...")
    except Exception as e:
        logger.critical(f"An unhandled error occurred: {e}", exc_info=True)
    finally:
        await system.stop_trading()

if __name__ == "__main__":
    asyncio.run(main())
