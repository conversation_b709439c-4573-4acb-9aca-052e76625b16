"""
Test script for Polars-based Technical Indicators Manager

This script validates:
1. Polars DataFrame operations for real-time data
2. polars-talib indicator calculations
3. Market regime detection
4. Data flow integration
"""

import asyncio
import logging
import sys
from datetime import datetime, timedelta
from pathlib import Path
import polars as pl
import polars_talib as plta

# Add project root to path
sys.path.append('.')

from agents.polars_technical_indicators_manager import PolarsTechnicalIndicatorsManager, MarketRegime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_polars_talib_basic():
    """Test basic polars-talib functionality"""
    logger.info("🧪 Testing polars-talib basic functionality...")
    
    try:
        # Create sample OHLC data
        df = pl.DataFrame({
            'timestamp': [datetime.now() - timedelta(minutes=i) for i in range(100, 0, -1)],
            'open': [19800 + i * 2 for i in range(100)],
            'high': [19850 + i * 2 for i in range(100)],
            'low': [19750 + i * 2 for i in range(100)],
            'close': [19820 + i * 2 for i in range(100)],
            'volume': [1000000 + i * 1000 for i in range(100)]
        })
        
        logger.info(f"✅ Created sample DataFrame with {len(df)} rows")
        
        # Test basic indicators
        df_with_indicators = df.with_columns([
            plta.sma(pl.col("close"), timeperiod=20).alias("sma_20"),
            plta.rsi(pl.col("close"), timeperiod=14).alias("rsi_14"),
            plta.macd(pl.col("close"), fastperiod=12, slowperiod=26, signalperiod=9).alias("macd_data")
        ])
        
        # Extract latest values
        latest = df_with_indicators.tail(1).to_dicts()[0]
        
        logger.info(f"✅ SMA 20: {latest.get('sma_20', 'N/A')}")
        logger.info(f"✅ RSI 14: {latest.get('rsi_14', 'N/A')}")
        logger.info(f"✅ MACD: {latest.get('macd_data', 'N/A')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ polars-talib basic test failed: {e}")
        return False


async def test_indicators_manager():
    """Test the Polars Technical Indicators Manager"""
    logger.info("🧪 Testing Polars Technical Indicators Manager...")
    
    try:
        # Initialize manager
        manager = PolarsTechnicalIndicatorsManager()
        
        # Generate sample data for NIFTY
        base_price = 19800
        sample_data = []
        
        for i in range(60):  # 60 data points for proper indicator calculation
            timestamp = datetime.now() - timedelta(minutes=60-i)
            price_change = (i - 30) * 5  # Create some trend
            noise = (i % 7 - 3) * 10  # Add some noise
            
            close_price = base_price + price_change + noise
            open_price = close_price - 5
            high_price = close_price + 15
            low_price = close_price - 10
            
            sample_data.append({
                'underlying': 'NIFTY',
                'timestamp': timestamp,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': 0  # Index has no volume
            })
        
        # Feed data to manager
        for data in sample_data:
            await manager.update_indicators('NIFTY', data)
        
        # Get market conditions
        conditions = manager.get_market_conditions('NIFTY')
        if conditions:
            logger.info(f"✅ Market Regime: {conditions.regime.value}")
            logger.info(f"✅ Trend Strength: {conditions.trend_strength:.2f}")
            logger.info(f"✅ Momentum State: {conditions.momentum_state}")
            logger.info(f"✅ RSI: {conditions.rsi:.2f}")
            logger.info(f"✅ SMA 20: ₹{conditions.sma_20:.2f}")
            logger.info(f"✅ Support: ₹{conditions.support_level:.2f}")
            logger.info(f"✅ Resistance: ₹{conditions.resistance_level:.2f}")
        else:
            logger.warning("⚠️ No market conditions available")
        
        # Get indicator values
        indicator_values = manager.get_indicator_values('NIFTY')
        logger.info(f"✅ Data Points: {indicator_values.get('data_points', 0)}")
        logger.info(f"✅ Latest Close: ₹{indicator_values.get('close', 0):.2f}")
        
        # Get data summary
        summary = manager.get_data_summary()
        logger.info(f"✅ Data Summary: {summary}")
        
        # Save market conditions
        await manager.save_market_conditions()
        logger.info("✅ Market conditions saved")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Indicators manager test failed: {e}")
        return False


async def test_real_time_updates():
    """Test real-time data updates"""
    logger.info("🧪 Testing real-time data updates...")
    
    try:
        manager = PolarsTechnicalIndicatorsManager()
        
        # Add initial data
        base_price = 19800
        for i in range(50):
            data = {
                'underlying': 'NIFTY',
                'timestamp': datetime.now() - timedelta(minutes=50-i),
                'open': base_price + i,
                'high': base_price + i + 20,
                'low': base_price + i - 15,
                'close': base_price + i + 5,
                'volume': 0
            }
            await manager.update_indicators('NIFTY', data)
        
        logger.info("✅ Added initial 50 data points")
        
        # Simulate real-time updates
        for i in range(5):
            current_price = base_price + 50 + i * 10
            data = {
                'underlying': 'NIFTY',
                'timestamp': datetime.now(),
                'open': current_price - 5,
                'high': current_price + 15,
                'low': current_price - 10,
                'close': current_price,
                'volume': 0
            }
            
            await manager.update_indicators('NIFTY', data)
            
            # Get updated conditions
            conditions = manager.get_market_conditions('NIFTY')
            if conditions:
                logger.info(f"✅ Update {i+1}: Price=₹{current_price:.2f}, Regime={conditions.regime.value}, RSI={conditions.rsi:.2f}")
            
            await asyncio.sleep(0.1)  # Simulate time between updates
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Real-time updates test failed: {e}")
        return False


async def test_market_regime_detection():
    """Test market regime detection with different scenarios"""
    logger.info("🧪 Testing market regime detection...")
    
    try:
        manager = PolarsTechnicalIndicatorsManager()
        
        # Test scenarios
        scenarios = [
            ("Strong Bull Trend", lambda i: 19000 + i * 20),  # Strong uptrend
            ("Strong Bear Trend", lambda i: 20000 - i * 15),  # Strong downtrend
            ("Sideways Market", lambda i: 19500 + (i % 10 - 5) * 5),  # Sideways
            ("Volatile Market", lambda i: 19500 + (i % 3 - 1) * 50)  # High volatility
        ]
        
        for scenario_name, price_func in scenarios:
            logger.info(f"Testing scenario: {scenario_name}")
            
            # Reset manager for each scenario
            manager = PolarsTechnicalIndicatorsManager()
            
            # Generate data for scenario
            for i in range(60):
                price = price_func(i)
                data = {
                    'underlying': 'NIFTY',
                    'timestamp': datetime.now() - timedelta(minutes=60-i),
                    'open': price - 5,
                    'high': price + 20,
                    'low': price - 15,
                    'close': price,
                    'volume': 0
                }
                await manager.update_indicators('NIFTY', data)
            
            # Check detected regime
            conditions = manager.get_market_conditions('NIFTY')
            if conditions:
                logger.info(f"  ✅ Detected: {conditions.regime.value} (Trend: {conditions.trend_strength:.2f})")
            else:
                logger.warning(f"  ⚠️ No regime detected for {scenario_name}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Market regime detection test failed: {e}")
        return False


async def main():
    """Run all tests"""
    logger.info("🚀 Starting Polars Technical Indicators Tests...")
    
    tests = [
        ("polars-talib Basic", test_polars_talib_basic),
        ("Indicators Manager", test_indicators_manager),
        ("Real-time Updates", test_real_time_updates),
        ("Market Regime Detection", test_market_regime_detection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Polars-based indicators are working correctly.")
        logger.info("📊 The system now uses only Polars, PyArrow, and polars-talib for technical analysis.")
    else:
        logger.warning(f"⚠️ {total - passed} test(s) failed. Please check the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
