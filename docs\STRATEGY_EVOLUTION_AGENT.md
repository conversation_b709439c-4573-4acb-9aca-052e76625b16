# 🧬 Options Strategy Evolution Agent

## 📋 Overview

The **Options Strategy Evolution Agent** is a comprehensive adaptive strategy optimization system that uses genetic algorithms, machine learning, and market regime detection to continuously evolve and improve trading strategies. Built specifically for Windows environments with Polars, PyArrow, and Polars-TA-Lib integration.

## ✅ Core Features Implemented

### 🔍 1. Underperforming Strategy Detection
- **Automated Monitoring**: Continuously monitors strategy performance metrics
- **Multi-Metric Analysis**: Tracks ROI, Sharpe ratio, win rate, drawdown, expectancy
- **Trend Analysis**: Detects performance degradation over time
- **Smart Flagging**: Automatically flags strategies for evolution based on configurable thresholds

**Example Detection**:
```
🚨 Strategy Strat_008 flagged for evolution:
- ROI below threshold: 2.1% < 5.0%
- Win rate declining: 42% (was 61%)
- Drawdown exceeded: 8.2% > 8.0%
```

### 🧪 2. Strategy Cloning & Mutation Engine
- **Genetic Algorithm**: Advanced GA with crossover, mutation, and selection
- **Multiple Mutation Types**: Conservative, aggressive, targeted, and random mutations
- **Parameter Evolution**: RSI periods, MA lengths, stop-loss/take-profit ratios
- **Smart Targeting**: Addresses specific performance issues with targeted mutations

**Mutation Examples**:
```yaml
Original: RSI(14) > 60 and IV < 20
Mutated:  RSI(9) > 55 and IV_Rank < 40

Original: Stop Loss: 25%, Take Profit: 50%
Mutated:  Stop Loss: 18%, Take Profit: 65%
```

### ⚡ 3. Strategy Evaluation Pipeline Integration
- **Automated Backtesting**: Seamless integration with backtesting agent
- **Performance Comparison**: Compares evolved strategies with parents
- **Statistical Validation**: Ensures sufficient sample size for evaluation
- **Version Tracking**: Maintains complete evolution history

### 🎯 4. Automated Promotion/Demotion Decisions
- **Performance-Based**: Promotes strategies exceeding thresholds
- **Consistency Checks**: Requires consistent performance over time
- **Automatic Demotion**: Demotes underperforming active strategies
- **Smart Cleanup**: Archives old disabled strategies

**Decision Matrix**:
```
✅ Promote: ROI ≥ 5%, Sharpe ≥ 0.5, Win Rate ≥ 45%, Consistent 3+ periods
⬇️ Demote: Declining performance, Below thresholds, Excessive drawdown
🗑️ Archive: Disabled > 30 days, No recent activity
```

### 🌊 5. Market-Regime Adaptation
- **Regime Detection**: Identifies 7 distinct market regimes
- **Adaptive Strategies**: Optimizes strategies for current regime
- **Regime-Specific Mutations**: Tailors evolution to market conditions
- **Performance Tracking**: Tracks strategy performance by regime

**Market Regimes**:
- 📈 **Trending Bull**: Long calls, bull spreads
- 📉 **Trending Bear**: Long puts, bear spreads  
- ➡️ **Sideways Low Vol**: Iron condors, butterflies
- 🌊 **Sideways High Vol**: Short straddles/strangles
- ⚡ **Volatile Uncertain**: Long straddles/strangles
- 🚀 **Breakout**: Momentum strategies
- 🔄 **Reversal**: Mean reversion strategies

### 🤝 6. Meta-Strategy Fusion (Ensemble)
- **Top Performer Identification**: Finds best performing strategies
- **Ensemble Creation**: Combines strategies using multiple methods
- **Weighted Voting**: Performance-weighted signal combination
- **Regime-Specific Ensembles**: Different combinations per market regime

### 🧠 7. LLM-Assisted Evolution Guidance
- **Natural Language Processing**: Interprets user feedback
- **Strategy Translation**: Converts descriptions to strategy logic
- **Evolution Explanations**: Provides human-readable evolution rationale
- **Interactive Feedback**: Incorporates user suggestions

### 📈 8. Performance Tagging and Metrics Logging
- **Comprehensive Tracking**: Logs every evolution step
- **Lineage Tracking**: Maintains complete strategy family trees
- **Performance Attribution**: Identifies what changes improved performance
- **Regime Sensitivity**: Tracks performance across different market conditions

### 🔬 9. Continuous Experimentation Framework
- **A/B Testing**: Tests strategies in simulation mode
- **Statistical Significance**: Ensures reliable results
- **Early Stopping**: Stops poor experiments early
- **Experiment Types**: Parameter optimization, regime adaptation, risk tuning

### 🔄 10. Self-Learning Feedback Loop
- **Reinforcement Learning**: Adapts based on live trading results
- **Pattern Recognition**: Identifies successful mutation patterns
- **Parameter Adaptation**: Adjusts mutation parameters based on success
- **Market Learning**: Learns from market condition changes

### 📝 11. Human-Readable Evolution Logs
- **Daily Summaries**: Clear evolution progress reports
- **Strategy Reports**: Detailed individual strategy evolution
- **Performance Insights**: Actionable insights from evolution data
- **Natural Language**: Easy-to-understand explanations

### 🗂️ 12. Strategy Version Registry
- **Centralized Storage**: Single source of truth for all strategies
- **Version Control**: Complete version history with branching
- **Metadata Tracking**: Tags, performance, regime sensitivity
- **Smart Cleanup**: Automatic archival of old versions

## 🎁 Bonus Features

### 📧 Notifications
- **Email Alerts**: SMTP-based email notifications
- **Telegram Integration**: Real-time Telegram bot notifications
- **Smart Triggers**: Configurable notification triggers
- **Rich Content**: Detailed performance summaries

### 🪟 Windows Optimizations
- **Multiprocessing**: Parallel evolution using multiple cores
- **Async I/O**: Non-blocking file operations
- **Memory Management**: Efficient memory usage patterns
- **Path Handling**: Windows-compatible path operations

### 📊 Advanced Analytics
- **Polars Integration**: High-performance data processing
- **PyArrow Backend**: Efficient columnar data operations
- **Technical Indicators**: Polars-TA-Lib integration
- **Streaming Processing**: Handle large datasets efficiently

## 🚀 Quick Start

### 1. Installation
```bash
# Install required packages
pip install polars pyarrow aiofiles aiohttp pyyaml

# Optional: Install polars-talib for technical indicators
pip install polars-talib
```

### 2. Configuration
```bash
# Copy and customize configuration
cp config/options_strategy_evolution_config.yaml.example config/options_strategy_evolution_config.yaml

# Edit configuration for your environment
notepad config/options_strategy_evolution_config.yaml
```

### 3. Run the Agent
```bash
# Start the evolution agent
python main.py --agent strategy_evolution

# Or run directly
python agents/options_strategy_evolution_agent.py
```

## 📊 Performance Metrics

The agent tracks comprehensive performance metrics:

- **Return Metrics**: ROI, Total Return, Risk-Adjusted Return
- **Risk Metrics**: Sharpe Ratio, Sortino Ratio, Calmar Ratio, Max Drawdown
- **Trade Metrics**: Win Rate, Profit Factor, Expectancy, Average Trade Duration
- **Volatility Metrics**: Strategy Volatility, Beta, Alpha
- **Options-Specific**: Greeks sensitivity, IV impact, Time decay effects

## 🔧 Configuration Options

### Genetic Algorithm Parameters
```yaml
genetic_algorithm:
  population_size: 50      # Strategy population size
  mutation_rate: 0.15      # 15% mutation probability
  crossover_rate: 0.8      # 80% crossover probability
  elite_percentage: 0.1    # Preserve top 10%
```

### Performance Thresholds
```yaml
performance_thresholds:
  min_roi: 0.05           # 5% minimum ROI
  min_sharpe: 0.5         # Minimum Sharpe ratio
  min_win_rate: 0.45      # 45% minimum win rate
  max_drawdown: 0.15      # 15% maximum drawdown
```

### Mutation Parameters
```yaml
mutation_parameters:
  rsi_range: [5, 25]      # RSI period range
  ma_range: [5, 50]       # MA period range
  stop_loss_range: [0.005, 0.05]  # Stop loss range
  take_profit_range: [0.01, 0.10] # Take profit range
```

## 📈 Evolution Process Flow

```mermaid
graph TD
    A[Strategy Performance Monitoring] --> B{Underperforming?}
    B -->|Yes| C[Flag for Evolution]
    B -->|No| A
    C --> D[Create Mutations]
    D --> E[Backtest Mutations]
    E --> F[Evaluate Performance]
    F --> G{Better than Parent?}
    G -->|Yes| H[Promote Strategy]
    G -->|No| I[Mark as Failed]
    H --> J[Update Registry]
    I --> J
    J --> A
```

## 🔍 Monitoring and Alerts

### Real-time Monitoring
- Strategy performance dashboards
- Evolution progress tracking
- Market regime detection
- Experiment status monitoring

### Alert Types
- 🎉 **Strategy Promoted**: New strategy outperforms parent
- ⬇️ **Strategy Demoted**: Active strategy underperforming
- 🌊 **Regime Change**: Market conditions shifted
- 🔬 **Experiment Complete**: A/B test results available
- 🚨 **Performance Alert**: Strategy below thresholds

## 📚 Advanced Usage

### Custom Mutation Functions
```python
async def custom_mutation(config: StrategyConfig, factor: float):
    # Implement custom mutation logic
    pass
```

### Regime-Specific Adaptations
```python
async def adapt_for_regime(strategy: StrategyConfig, regime: MarketRegime):
    # Implement regime-specific adaptations
    pass
```

### Performance Analysis
```python
# Analyze strategy lineage
lineage = await agent.get_strategy_lineage(strategy_id)

# Get regime performance
regime_perf = await agent.get_regime_performance(strategy_id)

# Evolution statistics
stats = await agent.get_evolution_statistics()
```

## 🛠️ Troubleshooting

### Common Issues
1. **No strategies found**: Ensure strategy generation agent has run
2. **Backtest integration**: Check backtesting agent configuration
3. **Notification failures**: Verify email/Telegram credentials
4. **Performance issues**: Adjust Windows optimization settings

### Debug Mode
```bash
# Run with debug logging
python main.py --agent strategy_evolution --debug

# Check logs
tail -f logs/strategy_evolution.log
```

## 📞 Support

For issues, questions, or feature requests:
- Check the logs in `logs/strategy_evolution.log`
- Review configuration in `config/options_strategy_evolution_config.yaml`
- Examine evolution history in `data/strategy_evolution/`

## 🔮 Future Enhancements

- **Deep Learning Integration**: Neural network-based strategy generation
- **Multi-Asset Evolution**: Cross-asset strategy optimization
- **Real-time Adaptation**: Intraday strategy adjustments
- **Cloud Integration**: Distributed evolution processing
- **Advanced Ensembles**: Dynamic ensemble rebalancing
