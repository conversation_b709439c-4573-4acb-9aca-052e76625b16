#!/usr/bin/env python3
"""
Comprehensive Test Suite for Options Trading System

Features:
🧪 1. Unit Tests
- Individual agent testing
- Options pricing model tests
- Greeks calculation validation
- Data processing tests

📊 2. Integration Tests
- Multi-agent workflow testing
- Data flow validation
- API integration tests
- End-to-end pipeline tests

⚡ 3. Performance Tests
- Speed benchmarks
- Memory usage tests
- Scalability tests
- Load testing

🎯 4. Strategy Validation Tests
- Strategy logic validation
- Backtesting accuracy tests
- Risk management tests
- P&L calculation tests
"""

import pytest
import asyncio
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, patch, AsyncMock

# Import agents for testing
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents.options_data_ingestion_agent import OptionsDataIngestionAgent
from agents.options_feature_engineering_agent import OptionsFeatureEngineeringAgent
from agents.options_strategy_generation_agent import OptionsStrategyGenerationAgent
from agents.options_backtesting_agent import OptionsBacktestingAgent
from agents.options_ai_training_agent import OptionsAITrainingAgent
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent
from agents.options_signal_generation_agent import OptionsSignalGenerationAgent
from agents.options_risk_management_agent import OptionsRiskManagementAgent
from agents.options_execution_agent import OptionsExecutionAgent
from agents.options_performance_analysis_agent import OptionsPerformanceAnalysisAgent
from agents.options_llm_interface_agent import OptionsLLMInterfaceAgent
from agents.options_strategy_evolution_agent import OptionsStrategyEvolutionAgent

from utils.options_pricing import OptionsBlackScholes, OptionsGreeks, ImpliedVolatility

class TestOptionsSystem:
    """Comprehensive test suite for the options trading system"""
    
    @pytest.fixture
    def temp_data_dir(self):
        """Create temporary data directory for testing"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_options_data(self):
        """Generate sample options data for testing"""
        data = []
        base_time = datetime.now()
        
        for i in range(100):
            timestamp = base_time + timedelta(minutes=i)
            
            for strike in [24000, 24500, 25000, 25500, 26000]:
                for option_type in ['CE', 'PE']:
                    data.append({
                        'timestamp': timestamp,
                        'symbol': f"NIFTY{strike}{option_type}",
                        'underlying': 'NIFTY',
                        'strike_price': strike,
                        'expiry_date': '2024-01-25',
                        'option_type': option_type,
                        'open': 100.0 + np.random.normal(0, 5),
                        'high': 105.0 + np.random.normal(0, 5),
                        'low': 95.0 + np.random.normal(0, 5),
                        'close': 100.0 + np.random.normal(0, 5),
                        'volume': np.random.randint(100, 1000),
                        'open_interest': np.random.randint(1000, 10000)
                    })
        
        return pl.DataFrame(data)

class TestOptionsPricing:
    """Test options pricing models and Greeks calculations"""
    
    def test_black_scholes_call_price(self):
        """Test Black-Scholes call option pricing"""
        S = 25000  # Current price
        K = 25000  # Strike price
        T = 0.25   # 3 months to expiry
        r = 0.06   # Risk-free rate
        sigma = 0.20  # Volatility
        
        call_price = OptionsBlackScholes.call_price(S, K, T, r, sigma)
        
        # Call price should be positive
        assert call_price > 0
        
        # ATM call should have significant time value
        assert call_price > 200  # Reasonable for Nifty options
        
        # Test with different strikes
        otm_call = OptionsBlackScholes.call_price(S, K + 500, T, r, sigma)
        itm_call = OptionsBlackScholes.call_price(S, K - 500, T, r, sigma)
        
        # ITM call should be more expensive than ATM, ATM more than OTM
        assert itm_call > call_price > otm_call
    
    def test_black_scholes_put_price(self):
        """Test Black-Scholes put option pricing"""
        S = 25000
        K = 25000
        T = 0.25
        r = 0.06
        sigma = 0.20
        
        put_price = OptionsBlackScholes.put_price(S, K, T, r, sigma)
        
        # Put price should be positive
        assert put_price > 0
        
        # Test put-call parity
        call_price = OptionsBlackScholes.call_price(S, K, T, r, sigma)
        
        # Put-Call Parity: C - P = S - K*e^(-rT)
        parity_diff = call_price - put_price - (S - K * np.exp(-r * T))
        assert abs(parity_diff) < 0.01  # Should be very close to zero
    
    def test_greeks_calculation(self):
        """Test Greeks calculations"""
        S = 25000
        K = 25000
        T = 0.25
        r = 0.06
        sigma = 0.20
        
        # Test Delta
        call_delta = OptionsGreeks.delta(S, K, T, r, sigma, 'call')
        put_delta = OptionsGreeks.delta(S, K, T, r, sigma, 'put')
        
        # ATM call delta should be around 0.5
        assert 0.4 < call_delta < 0.6
        
        # Put delta should be negative
        assert put_delta < 0
        
        # Delta relationship: call_delta - put_delta = 1
        assert abs((call_delta - put_delta) - 1.0) < 0.01
        
        # Test Gamma
        gamma = OptionsGreeks.gamma(S, K, T, r, sigma)
        assert gamma > 0  # Gamma is always positive
        
        # Test Theta
        call_theta = OptionsGreeks.theta(S, K, T, r, sigma, 'call')
        assert call_theta < 0  # Theta is usually negative (time decay)
        
        # Test Vega
        vega = OptionsGreeks.vega(S, K, T, r, sigma)
        assert vega > 0  # Vega is always positive
    
    def test_implied_volatility(self):
        """Test implied volatility calculation"""
        S = 25000
        K = 25000
        T = 0.25
        r = 0.06
        true_sigma = 0.20
        
        # Calculate theoretical price
        market_price = OptionsBlackScholes.call_price(S, K, T, r, true_sigma)
        
        # Calculate implied volatility
        implied_vol = ImpliedVolatility.calculate_iv(market_price, S, K, T, r, 'call')
        
        # Should recover the original volatility
        assert abs(implied_vol - true_sigma) < 0.01

class TestDataIngestionAgent:
    """Test data ingestion agent functionality"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, temp_data_dir):
        """Test agent initialization"""
        with patch('pathlib.Path') as mock_path:
            mock_path.return_value = temp_data_dir
            
            agent = OptionsDataIngestionAgent()
            
            # Test initialization
            result = await agent.initialize()
            assert result is True
            
            # Test configuration
            assert agent.config is not None
            assert 'NIFTY' in agent.config.underlying_symbols
            assert 'BANKNIFTY' in agent.config.underlying_symbols
    
    @pytest.mark.asyncio
    async def test_multi_timeframe_generation(self, sample_options_data):
        """Test multi-timeframe data generation"""
        agent = OptionsDataIngestionAgent()
        
        # Test resampling to 5-minute data
        resampled_data = await agent._resample_data(sample_options_data, 5)
        
        assert resampled_data is not None
        assert resampled_data.height > 0
        assert resampled_data.height < sample_options_data.height  # Should be fewer bars

class TestFeatureEngineeringAgent:
    """Test feature engineering agent functionality"""
    
    @pytest.mark.asyncio
    async def test_greeks_calculation(self, sample_options_data):
        """Test Greeks calculation in feature engineering"""
        agent = OptionsFeatureEngineeringAgent()
        await agent.initialize()
        
        greeks_data = await agent._calculate_greeks(sample_options_data)
        
        assert greeks_data is not None
        assert greeks_data.height > 0
        
        # Check that Greeks columns exist
        expected_cols = ['delta', 'gamma', 'theta', 'vega', 'rho', 'implied_vol']
        for col in expected_cols:
            assert col in greeks_data.columns
    
    @pytest.mark.asyncio
    async def test_volatility_metrics(self, sample_options_data):
        """Test volatility metrics calculation"""
        agent = OptionsFeatureEngineeringAgent()
        await agent.initialize()
        
        vol_data = await agent._calculate_volatility_metrics(sample_options_data)
        
        assert vol_data is not None
        assert vol_data.height > 0
        
        # Check volatility columns
        expected_cols = ['historical_vol', 'current_vol', 'vol_rank']
        for col in expected_cols:
            assert col in vol_data.columns

class TestStrategyGeneration:
    """Test strategy generation functionality"""
    
    @pytest.mark.asyncio
    async def test_long_call_strategy_generation(self, sample_options_data):
        """Test long call strategy generation"""
        agent = OptionsStrategyGenerationAgent()
        await agent.initialize()
        
        # Mock option chain data
        agent.market_data_cache['NIFTY'] = sample_options_data
        
        strategies = await agent._generate_long_call_strategies('NIFTY', sample_options_data, 25000)
        
        assert len(strategies) > 0
        
        for strategy in strategies:
            assert strategy.strategy_type.value == 'long_call'
            assert len(strategy.legs) == 1
            assert strategy.legs[0].quantity > 0  # Long position

class TestBacktestingEngine:
    """Test backtesting engine functionality"""
    
    @pytest.mark.asyncio
    async def test_backtest_execution(self, sample_options_data, temp_data_dir):
        """Test backtest execution"""
        agent = OptionsBacktestingAgent()
        await agent.initialize()
        
        # Create sample strategy
        sample_strategy = {
            'strategy_id': 'test_strategy',
            'strategy_type': 'long_call',
            'underlying': 'NIFTY',
            'legs': [{
                'symbol': 'NIFTY25000CE',
                'option_type': 'CE',
                'strike_price': 25000,
                'quantity': 1,
                'premium': 100
            }],
            'max_loss': 100
        }
        
        # Test strategy execution
        result = agent._backtest_strategy(sample_strategy, sample_options_data.to_pandas())
        
        assert result is not None
        assert hasattr(result, 'strategy_id')
        assert result.strategy_id == 'test_strategy'

class TestPerformanceMetrics:
    """Test performance measurement and benchmarks"""
    
    def test_data_processing_speed(self, sample_options_data):
        """Test data processing speed benchmarks"""
        import time
        
        # Test Polars performance
        start_time = time.time()
        
        # Perform typical operations
        result = (
            sample_options_data
            .filter(pl.col('underlying') == 'NIFTY')
            .group_by(['strike_price', 'option_type'])
            .agg([
                pl.col('volume').sum(),
                pl.col('close').mean()
            ])
        )
        
        processing_time = time.time() - start_time
        
        # Should process quickly
        assert processing_time < 1.0  # Less than 1 second
        assert result.height > 0
    
    def test_memory_usage(self, sample_options_data):
        """Test memory usage efficiency"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Perform memory-intensive operations
        large_data = sample_options_data
        for _ in range(10):
            large_data = pl.concat([large_data, sample_options_data])
        
        # Process the large dataset
        result = large_data.group_by('underlying').agg([
            pl.col('volume').sum(),
            pl.col('close').mean()
        ])
        
        final_memory = process.memory_info().rss
        memory_increase = (final_memory - initial_memory) / 1024 / 1024  # MB
        
        # Memory increase should be reasonable
        assert memory_increase < 500  # Less than 500MB increase
        assert result.height > 0

class TestIntegration:
    """Integration tests for the complete system"""
    
    @pytest.mark.asyncio
    async def test_agent_communication(self):
        """Test communication between agents"""
        # This would test the complete workflow
        # For now, just test that agents can be initialized
        
        agents = [
            OptionsDataIngestionAgent(),
            OptionsFeatureEngineeringAgent(),
            OptionsStrategyGenerationAgent(),
            OptionsBacktestingAgent()
        ]
        
        for agent in agents:
            result = await agent.initialize()
            assert result is True
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, temp_data_dir):
        """Test complete end-to-end workflow"""
        # This would test the complete pipeline
        # For now, just verify the main components exist
        
        from main import OptionsSystemOrchestrator
        
        orchestrator = OptionsSystemOrchestrator()
        
        # Test that orchestrator can be created
        assert orchestrator is not None
        assert hasattr(orchestrator, 'run_agent')
        assert hasattr(orchestrator, 'run_workflow')

# Performance benchmarks
class TestPerformanceBenchmarks:
    """Performance benchmarks for the system"""
    
    def test_options_pricing_speed(self):
        """Benchmark options pricing calculations"""
        import time
        
        # Test vectorized pricing
        S = np.full(1000, 25000)
        K = np.random.uniform(24000, 26000, 1000)
        T = np.full(1000, 0.25)
        r = 0.06
        sigma = np.full(1000, 0.20)
        
        start_time = time.time()
        
        from utils.options_pricing import vectorized_black_scholes
        prices = vectorized_black_scholes(S, K, T, r, sigma, 'call')
        
        calculation_time = time.time() - start_time
        
        # Should calculate 1000 options quickly
        assert calculation_time < 0.1  # Less than 100ms
        assert len(prices) == 1000
        assert all(price >= 0 for price in prices)

# Test configuration
pytest_plugins = []

def pytest_configure(config):
    """Configure pytest"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
