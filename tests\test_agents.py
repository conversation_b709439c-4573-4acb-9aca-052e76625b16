#!/usr/bin/env python3
"""
Test script for Options Strategy Generation and Feature Engineering Agents
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta
from pathlib import Path
import sys
import os

# Add the agents directory to the path
sys.path.append(str(Path(__file__).parent / "agents"))

from options_strategy_generation_agent import OptionsStrategyGenerationAgent, StrategyType
from options_feature_engineering_agent import OptionsFeatureEngineeringAgent

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_sample_option_chain_data():
    """Create sample option chain data for testing"""
    
    # Sample NIFTY option chain data
    sample_data = []
    spot_price = 25000
    
    # Generate strikes around spot price
    strikes = list(range(24000, 26000, 100))
    
    for strike in strikes:
        # Call option
        call_premium = max(1, spot_price - strike + 50) if strike <= spot_price else max(1, 50 - (strike - spot_price) * 0.5)
        sample_data.append({
            'symbol': f'NIFTY{strike}CE',
            'underlying': 'NIFTY',
            'option_type': 'CE',
            'strike_price': strike,
            'expiry_date': '2024-01-25',
            'ltp': call_premium,
            'open_interest': 1000 + (abs(strike - spot_price) * 10),
            'volume': 500 + (abs(strike - spot_price) * 5),
            'timestamp': datetime.now(),
            'high': call_premium * 1.1,
            'low': call_premium * 0.9,
            'close': call_premium,
            'underlying_price': spot_price
        })
        
        # Put option
        put_premium = max(1, strike - spot_price + 50) if strike >= spot_price else max(1, 50 - (spot_price - strike) * 0.5)
        sample_data.append({
            'symbol': f'NIFTY{strike}PE',
            'underlying': 'NIFTY',
            'option_type': 'PE',
            'strike_price': strike,
            'expiry_date': '2024-01-25',
            'ltp': put_premium,
            'open_interest': 1000 + (abs(strike - spot_price) * 10),
            'volume': 500 + (abs(strike - spot_price) * 5),
            'timestamp': datetime.now(),
            'high': put_premium * 1.1,
            'low': put_premium * 0.9,
            'close': put_premium,
            'underlying_price': spot_price
        })
    
    return pl.DataFrame(sample_data)

def create_sample_historical_data():
    """Create sample historical data for testing"""
    
    # Generate 100 days of sample data
    dates = [datetime.now() - timedelta(days=i) for i in range(100, 0, -1)]
    
    sample_data = []
    base_price = 25000
    
    for i, date in enumerate(dates):
        # Simulate price movement
        price_change = (i % 10 - 5) * 50  # Random-ish price movement
        current_price = base_price + price_change
        
        sample_data.append({
            'symbol': 'NIFTY25000CE',
            'underlying': 'NIFTY',
            'option_type': 'CE',
            'strike_price': 25000,
            'expiry_date': '2024-01-25',
            'timestamp': date,
            'open': current_price,
            'high': current_price * 1.02,
            'low': current_price * 0.98,
            'close': current_price,
            'volume': 1000 + (i * 10),
            'open_interest': 5000 + (i * 50),
            'underlying_price': base_price + price_change
        })
    
    return pl.DataFrame(sample_data)

async def test_strategy_generation_agent():
    """Test the Options Strategy Generation Agent"""
    logger.info("Testing Options Strategy Generation Agent...")
    
    try:
        # Create sample data
        option_chain = create_sample_option_chain_data()
        
        # Save sample data to test directory
        test_data_path = Path("data/option_chains")
        test_data_path.mkdir(parents=True, exist_ok=True)
        
        chain_file = test_data_path / "NIFTY_test_chain_20240125.parquet"
        option_chain.write_parquet(chain_file)
        
        # Initialize and test the agent
        agent = OptionsStrategyGenerationAgent()
        
        # Test initialization
        success = await agent.initialize()
        if not success:
            logger.error("Failed to initialize Strategy Generation Agent")
            return False
        
        # Test strategy generation
        success = await agent.start()
        if not success:
            logger.error("Failed to start Strategy Generation Agent")
            return False
        
        logger.info("Strategy Generation Agent test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Strategy Generation Agent test failed: {e}")
        return False
    finally:
        try:
            await agent.cleanup()
        except:
            pass

async def test_feature_engineering_agent():
    """Test the Options Feature Engineering Agent"""
    logger.info("Testing Options Feature Engineering Agent...")
    
    try:
        # Create sample data
        historical_data = create_sample_historical_data()
        
        # Save sample data to test directory
        test_data_path = Path("data/historical/5min")
        test_data_path.mkdir(parents=True, exist_ok=True)
        
        hist_file = test_data_path / "NIFTY_5min_test.parquet"
        historical_data.write_parquet(hist_file)
        
        # Initialize and test the agent
        agent = OptionsFeatureEngineeringAgent()
        
        # Test initialization
        success = await agent.initialize()
        if not success:
            logger.error("Failed to initialize Feature Engineering Agent")
            return False
        
        # Test feature engineering
        success = await agent.start()
        if not success:
            logger.error("Failed to start Feature Engineering Agent")
            return False
        
        logger.info("Feature Engineering Agent test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Feature Engineering Agent test failed: {e}")
        return False
    finally:
        try:
            await agent.cleanup()
        except:
            pass

async def test_strategy_types():
    """Test that all strategy types are properly defined"""
    logger.info("Testing strategy types...")
    
    try:
        # Test that all strategy types can be instantiated
        strategy_types = [
            StrategyType.LONG_CALL,
            StrategyType.ATM_LONG_CALL,
            StrategyType.OTM_LONG_CALL,
            StrategyType.FAR_OTM_LONG_CALL,
            StrategyType.LONG_STRADDLE,
            StrategyType.IRON_CONDOR,
            StrategyType.BUTTERFLY_SPREAD,
            StrategyType.WEEKLY_EXPIRY_STRADDLE
        ]
        
        for strategy_type in strategy_types:
            logger.info(f"Strategy type {strategy_type.value} is properly defined")
        
        logger.info(f"All {len(strategy_types)} strategy types tested successfully")
        return True
        
    except Exception as e:
        logger.error(f"Strategy types test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("Starting Options Agents Test Suite...")
    
    # Test strategy types
    success1 = await test_strategy_types()
    
    # Test strategy generation agent
    success2 = await test_strategy_generation_agent()
    
    # Test feature engineering agent
    success3 = await test_feature_engineering_agent()
    
    # Summary
    if success1 and success2 and success3:
        logger.info("✅ All tests passed successfully!")
        return True
    else:
        logger.error("❌ Some tests failed!")
        return False

if __name__ == "__main__":
    asyncio.run(main())
