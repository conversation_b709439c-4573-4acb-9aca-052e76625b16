# 🚦 Live Trading System - Complete Implementation

## Overview

The Live Trading System is a comprehensive automated options trading platform that supports both paper trading (virtual money) and real money trading via Angel One SmartAPI. The system integrates multiple AI agents, advanced risk management, real-time market monitoring, and LLM-powered insights.

## ✅ Implemented Features

### 🔧 Launch Modes
- **Paper Trading Mode** (`--paper_trading`): Virtual trading with ₹1,00,000 starting capital using live market data
- **Real Trading Mode** (`--real`): Live trading with real money via Angel One SmartAPI
- **Configuration-based Mode**: Set mode in `config/live_trading_config.yaml`

### ✅ System Initialization
- **All Agents Loaded**: Market Monitoring, Signal Generation, Risk Management, Execution, Performance Analysis, AI Training, LLM Interface
- **Configuration Management**: Comprehensive YAML-based configuration with validation
- **Enhanced Logging**: Specialized loggers for trades, signals, risk events, performance, and errors
- **Directory Structure**: Automatic creation of required directories

### ✅ Real-Time Market Data Listener
- **WebSocket Integration**: Connects to Angel One WebSocket feed
- **Multi-timeframe Support**: 1min, 3min, 5min, 15min data streams
- **Option Chain Monitoring**: Real-time option strikes and Greeks
- **Market Regime Detection**: Volatility and trend analysis

### ✅ Trade Signal Trigger Loop
- **Signal Processing**: Evaluates signals from multiple strategies
- **AI Integration**: ML model predictions and confidence scoring
- **Risk Evaluation**: Comprehensive risk checks before execution
- **Multi-mode Execution**: Paper trading simulation or real order placement

### ✅ Paper Trading Mode Logic
- **Virtual Portfolio**: Maintains ₹1,00,000 virtual balance
- **Realistic Simulation**: Includes brokerage, slippage, and market impact
- **Position Tracking**: Real-time P&L updates and SL/TP monitoring
- **Trade Logging**: Comprehensive trade history and performance metrics

### ✅ Real Trading Mode Logic
- **SmartAPI Integration**: Authenticates and places real orders
- **Order Management**: Tracks fills, slippage, margin requirements
- **Position Monitoring**: Real-time position and P&L tracking
- **Risk Controls**: Capital allocation and position size limits

### ✅ Live Position & Trade Management
- **Real-time Monitoring**: Current positions, P&L, and exposure
- **Auto Square-off**: End-of-day position closure
- **SL/TP Management**: Automatic stop-loss and take-profit execution
- **Performance Tracking**: Strategy-wise performance metrics

### ✅ Logging & Reporting
- **Comprehensive Logging**: Specialized loggers for different components
- **Trade Logs**: Detailed trade execution and P&L records
- **Signal Logs**: Signal generation and processing history
- **Risk Logs**: Risk events and safety alerts
- **Performance Logs**: Real-time performance metrics
- **Error Logs**: System errors and exceptions

### ✅ Daily Summary Generator
- **End-of-day Reports**: Comprehensive trading session summaries
- **Performance Metrics**: ROI, win rate, Sharpe ratio, max drawdown
- **Strategy Analysis**: Best/worst performing strategies
- **LLM Insights**: Natural language performance explanations
- **Export Functionality**: JSON reports in `exports/reports/`

### ✅ CLI Interface & Configuration
- **Command Line**: `python run_live_trading.py --paper_trading` or `--real`
- **YAML Configuration**: Comprehensive configuration in `config/live_trading_config.yaml`
- **Runtime Validation**: Configuration validation and error handling
- **Default Fallbacks**: Sensible defaults if configuration is missing

### ✅ Safety & Fail-Safes
- **API Monitoring**: Detects disconnection and data freeze
- **Drawdown Protection**: Stops trading on excessive capital loss
- **Consecutive SL Monitoring**: Halts trading after multiple stop-losses
- **Market Event Detection**: Extreme volatility and VIX spike protection
- **System Health**: Memory and CPU usage monitoring
- **Risk Alerts**: Real-time risk notifications

### ✅ LLM Features
- **Market Insights**: Periodic market analysis and recommendations
- **Trade Explanations**: Natural language trade decision explanations
- **Interactive Queries**: Ask questions about performance and strategies
- **Position Analysis**: LLM-powered position recommendations
- **Alert Processing**: Intelligent alert summarization

### ✅ Performance Monitoring
- **Real-time Metrics**: Continuous performance calculation
- **Comprehensive Analytics**: ROI, Sharpe ratio, profit factor, max drawdown
- **Performance Alerts**: Degradation warnings and notifications
- **Benchmarking**: Performance comparison against NIFTY
- **Historical Tracking**: Performance trend analysis

## 🚀 Quick Start

### 1. Installation
```bash
# Install required dependencies
pip install polars pyarrow aiofiles pyyaml langchain-ollama psutil

# Ensure all agent files are in the agents/ directory
# Ensure configuration file exists at config/live_trading_config.yaml
```

### 2. Configuration
Edit `config/live_trading_config.yaml` to customize:
- Trading capital and risk parameters
- Market settings (lot sizes, margins)
- Safety thresholds and limits
- LLM integration settings
- Logging preferences

### 3. Testing
```bash
# Run comprehensive tests
python test_live_trading.py
```

### 4. Paper Trading
```bash
# Start paper trading mode
python run_live_trading.py --paper_trading
```

### 5. Real Trading (when ready)
```bash
# Start real trading mode (requires SmartAPI credentials)
python run_live_trading.py --real
```

## 📁 Directory Structure
```
Option/
├── run_live_trading.py          # Main trading system
├── test_live_trading.py         # Test suite
├── config/
│   └── live_trading_config.yaml # Configuration file
├── agents/                      # Trading agents
├── logs/                        # Log files
│   ├── trades/                  # Trade logs
│   ├── signals/                 # Signal logs
│   ├── risk/                    # Risk event logs
│   ├── performance/             # Performance logs
│   └── errors/                  # Error logs
├── data/                        # Market data and insights
│   └── llm_insights/           # LLM-generated insights
└── exports/                     # Reports and summaries
    └── reports/                 # Daily summary reports
```

## ⚙️ Configuration Options

### Trading Settings
- `mode`: "paper" or "real"
- `capital`: Starting capital amount
- `run_duration`: Trading hours (e.g., "09:15 to 15:15")
- `signal_check_interval_seconds`: Signal processing frequency

### Risk Management
- `max_drawdown_percent`: Maximum allowed drawdown
- `consecutive_sl_hits_limit`: Stop trading after N consecutive SLs
- `max_positions_per_underlying`: Position concentration limits
- `max_capital_per_trade_percent`: Maximum capital per trade

### Paper Trading
- `brokerage_rate`: Simulated brokerage (default: 0.03%)
- `slippage_rate`: Simulated slippage (default: 0.1%)
- `enable_realistic_fills`: Enable market impact simulation

### LLM Integration
- `enable_llm_summary`: Enable/disable LLM features
- `llm_query_interval`: Frequency of LLM market insights
- `llm_models`: Model configuration for different purposes

## 🛡️ Safety Features

### Automatic Trading Halt Conditions
1. **API Disconnection**: No market data for 5+ minutes
2. **Excessive Drawdown**: Capital loss exceeds configured limit
3. **Consecutive Stop Losses**: Multiple SLs in a row
4. **Extreme Volatility**: VIX spike above threshold
5. **System Resource Issues**: High memory/CPU usage

### Risk Controls
- Position size limits per underlying
- Capital allocation limits per trade
- Real-time P&L monitoring
- Automatic position square-off at EOD

## 📊 Performance Metrics

### Real-time Calculations
- Return on Investment (ROI)
- Win Rate and Profit Factor
- Sharpe Ratio and Max Drawdown
- Average Win/Loss ratios
- Strategy-wise performance

### Reporting
- Daily summary reports with LLM insights
- Real-time performance logging
- Historical performance tracking
- Benchmark comparison (vs NIFTY)

## 🧠 LLM Integration

### Features
- Market condition analysis
- Trade decision explanations
- Position recommendations
- Performance insights
- Interactive Q&A

### Usage
```python
# Ask LLM questions
response = await system.ask_llm("How is the market performing today?")

# Get trade explanations
explanation = await system.explain_trade_decision(signal_data)
```

## 🔧 Windows Compatibility

The system is optimized for Windows environments:
- UTF-8 encoding for emoji support
- Windows-specific path handling
- Console output optimization
- File system compatibility

## 📝 Logging

### Log Types
- **Main Log**: `logs/live_trading.log` - General system events
- **Trade Log**: `logs/trades/trades.log` - All trade executions
- **Signal Log**: `logs/signals/signals.log` - Signal processing
- **Risk Log**: `logs/risk/risk.log` - Risk events and alerts
- **Performance Log**: `logs/performance/performance.log` - Metrics
- **Error Log**: `logs/errors/errors.log` - System errors

### Log Rotation
- Automatic file rotation when size exceeds 100MB
- Keeps 5 backup files
- UTF-8 encoding for emoji support

## 🚨 Troubleshooting

### Common Issues
1. **Agent Import Errors**: Ensure all agent files are in `agents/` directory
2. **Configuration Errors**: Check YAML syntax and required parameters
3. **Permission Errors**: Ensure write permissions for logs and data directories
4. **Memory Issues**: Monitor system resources, adjust position limits

### Debug Mode
Enable debug logging in configuration:
```yaml
log_level: "DEBUG"
development:
  debug_mode: true
```

## 🎯 Next Steps

1. **Test Thoroughly**: Run extensive paper trading tests
2. **Configure Risk Parameters**: Adjust safety limits for your risk tolerance
3. **Monitor Performance**: Review daily summaries and performance metrics
4. **Optimize Strategies**: Use LLM insights to improve trading strategies
5. **Scale Gradually**: Start with small position sizes in real trading

## 📞 Support

For issues or questions:
1. Check the logs for detailed error information
2. Review configuration settings
3. Run the test suite to identify problems
4. Ensure all dependencies are properly installed

---

**⚠️ Important**: Always test thoroughly in paper trading mode before using real money. Trading involves risk, and past performance does not guarantee future results.
