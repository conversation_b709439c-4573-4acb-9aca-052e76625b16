{"timestamp": "20250719_154854", "total_models": 0, "models": {}, "config": {"target_variables": ["option_price", "implied_vol", "strategy_return", "annualized_return", "sharpe_ratio"], "feature_columns": ["delta", "gamma", "theta", "vega", "iv_rank", "index_price", "index_return_1min", "index_return_3min", "index_return_5min", "index_return_15min", "index_volatility", "index_volatility_10", "index_volatility_50", "index_momentum_10", "index_momentum_20", "index_above_sma20", "index_above_sma50", "moneyness", "moneyness_deviation", "distance_from_atm", "is_itm", "time_to_expiry", "delta_exposure", "delta_pnl_1min", "gamma_risk", "vega_exposure", "theta_decay_rate", "is_pe", "is_ce", "direction_alignment", "rsi", "sma_20", "ema_20", "volume_ratio", "momentum"], "model_types": ["lightgbm", "tabnet"], "cv_folds": 5, "optuna_trials": 50, "use_index_reference": true, "pe_ce_filter": true, "timeframes": ["1min", "3min", "5min", "15min"], "train_ensemble": true}, "training_summary": {"timeframes_trained": [], "targets_trained": [], "model_types_used": [], "pe_ce_focused": true, "index_reference_used": true}}