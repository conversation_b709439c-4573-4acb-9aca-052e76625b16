#!/usr/bin/env python3
"""
🧬 Test Script for Options Strategy Evolution Agent

This script tests the basic functionality of the Strategy Evolution Agent
to ensure all components are working correctly.
"""

import asyncio
import sys
import os
import json
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import the agent
sys.path.append(str(Path(__file__).parent.parent))

from agents.options_strategy_evolution_agent import (
    OptionsStrategyEvolutionAgent,
    StrategyConfig,
    StrategyStatus,
    StrategyMetrics,
    MarketRegime,
    EvolutionReason
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StrategyEvolutionAgentTester:
    """🧪 Test class for Strategy Evolution Agent"""
    
    def __init__(self):
        self.agent = None
        self.test_results = {}
    
    async def run_all_tests(self):
        """🚀 Run all tests"""
        logger.info("🧪 Starting Strategy Evolution Agent Tests...")
        
        try:
            # Test 1: Agent Initialization
            await self.test_agent_initialization()
            
            # Test 2: Configuration Loading
            await self.test_configuration_loading()
            
            # Test 3: Strategy Registry
            await self.test_strategy_registry()
            
            # Test 4: Performance Detection
            await self.test_performance_detection()
            
            # Test 5: Strategy Mutation
            await self.test_strategy_mutation()
            
            # Test 6: Market Regime Detection
            await self.test_market_regime_detection()
            
            # Test 7: Notification System
            await self.test_notification_system()
            
            # Test 8: Data Persistence
            await self.test_data_persistence()
            
            # Print test results
            await self.print_test_results()
            
        except Exception as e:
            logger.error(f"❌ Test suite failed: {e}")
            return False
        
        finally:
            if self.agent:
                await self.agent.cleanup()
        
        return True
    
    async def test_agent_initialization(self):
        """🧪 Test agent initialization"""
        logger.info("🧪 Testing agent initialization...")
        
        try:
            self.agent = OptionsStrategyEvolutionAgent()
            success = await self.agent.initialize()
            
            self.test_results['initialization'] = {
                'success': success,
                'config_loaded': self.agent.config is not None,
                'paths_created': all(path.exists() for path in [
                    self.agent.evolution_path,
                    self.agent.registry_path,
                    self.agent.experiments_path,
                    self.agent.logs_path
                ])
            }
            
            logger.info("✅ Agent initialization test passed")
            
        except Exception as e:
            logger.error(f"❌ Agent initialization test failed: {e}")
            self.test_results['initialization'] = {'success': False, 'error': str(e)}
    
    async def test_configuration_loading(self):
        """🧪 Test configuration loading"""
        logger.info("🧪 Testing configuration loading...")
        
        try:
            config = self.agent.config
            
            required_sections = [
                'genetic_algorithm',
                'performance_thresholds',
                'evolution_intervals',
                'mutation_parameters',
                'notifications'
            ]
            
            sections_present = all(section in config for section in required_sections)
            
            self.test_results['configuration'] = {
                'success': sections_present,
                'sections_present': sections_present,
                'population_size': config.get('genetic_algorithm', {}).get('population_size', 0),
                'mutation_rate': config.get('genetic_algorithm', {}).get('mutation_rate', 0)
            }
            
            logger.info("✅ Configuration loading test passed")
            
        except Exception as e:
            logger.error(f"❌ Configuration loading test failed: {e}")
            self.test_results['configuration'] = {'success': False, 'error': str(e)}
    
    async def test_strategy_registry(self):
        """🧪 Test strategy registry functionality"""
        logger.info("🧪 Testing strategy registry...")
        
        try:
            # Create a test strategy
            test_strategy = StrategyConfig(
                strategy_id="test_strategy_001",
                name="Test Long Call Strategy",
                description="Test strategy for evolution agent",
                parameters={"strike_selection": "otm_10_percent"},
                entry_conditions=["rsi_14 < 30", "iv_rank < 50"],
                exit_conditions=["profit_target: 100%", "stop_loss: 50%"],
                risk_management={"stop_loss": 0.5, "take_profit": 1.0},
                market_outlook="bullish",
                volatility_outlook="neutral",
                timeframe="15min",
                status=StrategyStatus.ACTIVE
            )
            
            # Add to registry
            self.agent.strategy_registry[test_strategy.strategy_id] = test_strategy
            
            # Save and reload
            await self.agent._save_strategy_registry()
            
            # Clear registry and reload
            self.agent.strategy_registry.clear()
            await self.agent._load_strategy_registry()
            
            # Check if strategy was persisted
            strategy_persisted = test_strategy.strategy_id in self.agent.strategy_registry
            
            self.test_results['strategy_registry'] = {
                'success': strategy_persisted,
                'strategy_count': len(self.agent.strategy_registry),
                'test_strategy_found': strategy_persisted
            }
            
            logger.info("✅ Strategy registry test passed")
            
        except Exception as e:
            logger.error(f"❌ Strategy registry test failed: {e}")
            self.test_results['strategy_registry'] = {'success': False, 'error': str(e)}
    
    async def test_performance_detection(self):
        """🧪 Test performance detection"""
        logger.info("🧪 Testing performance detection...")
        
        try:
            # Create mock backtest data
            mock_backtest_data = {
                "test_strategy_001": {
                    "total_return": 0.02,  # Below threshold
                    "sharpe_ratio": 0.3,   # Below threshold
                    "win_rate": 0.4,       # Below threshold
                    "max_drawdown": -0.2,  # Above threshold
                    "expectancy": 0.01,    # Below threshold
                    "total_trades": 15
                }
            }
            
            # Save mock data
            mock_file = self.agent.backtest_path / "backtest_results_test.json"
            mock_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(mock_file, 'w') as f:
                json.dump(mock_backtest_data, f)
            
            # Test detection
            underperformers = await self.agent._detect_underperforming_strategies()
            
            self.test_results['performance_detection'] = {
                'success': len(underperformers) > 0,
                'underperformers_found': len(underperformers),
                'test_strategy_flagged': "test_strategy_001" in underperformers
            }
            
            logger.info("✅ Performance detection test passed")
            
        except Exception as e:
            logger.error(f"❌ Performance detection test failed: {e}")
            self.test_results['performance_detection'] = {'success': False, 'error': str(e)}
    
    async def test_strategy_mutation(self):
        """🧪 Test strategy mutation"""
        logger.info("🧪 Testing strategy mutation...")
        
        try:
            if "test_strategy_001" in self.agent.strategy_registry:
                parent_strategy = self.agent.strategy_registry["test_strategy_001"]
                
                # Create mutations
                mutations = await self.agent._create_strategy_mutations(
                    parent_strategy.strategy_id, 
                    ["roi_below_threshold", "sharpe_below_threshold"]
                )
                
                self.test_results['strategy_mutation'] = {
                    'success': len(mutations) > 0,
                    'mutations_created': len(mutations),
                    'mutation_types': [m.tags for m in mutations if hasattr(m, 'tags')]
                }
                
                logger.info("✅ Strategy mutation test passed")
            else:
                self.test_results['strategy_mutation'] = {
                    'success': False, 
                    'error': 'No test strategy found for mutation'
                }
                
        except Exception as e:
            logger.error(f"❌ Strategy mutation test failed: {e}")
            self.test_results['strategy_mutation'] = {'success': False, 'error': str(e)}
    
    async def test_market_regime_detection(self):
        """🧪 Test market regime detection"""
        logger.info("🧪 Testing market regime detection...")
        
        try:
            # Test different market conditions
            test_conditions = [
                {
                    'volatility': 0.1,
                    'trend_strength': 0.8,
                    'volume_ratio': 1.2,
                    'price_change_1d': 0.02,
                    'expected_regime': MarketRegime.TRENDING_BULL
                },
                {
                    'volatility': 0.3,
                    'trend_strength': 0.1,
                    'volume_ratio': 1.8,
                    'price_change_1d': 0.01,
                    'expected_regime': MarketRegime.VOLATILE_UNCERTAIN
                },
                {
                    'volatility': 0.12,
                    'trend_strength': 0.2,
                    'volume_ratio': 0.9,
                    'price_change_1d': 0.005,
                    'expected_regime': MarketRegime.SIDEWAYS_LOW_VOL
                }
            ]
            
            correct_detections = 0
            for condition in test_conditions:
                detected_regime = await self.agent._detect_market_regime(condition)
                if detected_regime == condition['expected_regime']:
                    correct_detections += 1
            
            self.test_results['market_regime_detection'] = {
                'success': correct_detections >= 2,  # At least 2 out of 3 correct
                'correct_detections': correct_detections,
                'total_tests': len(test_conditions)
            }
            
            logger.info("✅ Market regime detection test passed")
            
        except Exception as e:
            logger.error(f"❌ Market regime detection test failed: {e}")
            self.test_results['market_regime_detection'] = {'success': False, 'error': str(e)}
    
    async def test_notification_system(self):
        """🧪 Test notification system"""
        logger.info("🧪 Testing notification system...")
        
        try:
            # Test notification without actually sending
            await self.agent._send_notification(
                "Test Notification", 
                "This is a test notification from the evolution agent"
            )
            
            self.test_results['notification_system'] = {
                'success': True,  # If no exception, consider it successful
                'email_configured': self.agent.notifications.get('email_enabled', False),
                'telegram_configured': self.agent.notifications.get('telegram_enabled', False)
            }
            
            logger.info("✅ Notification system test passed")
            
        except Exception as e:
            logger.error(f"❌ Notification system test failed: {e}")
            self.test_results['notification_system'] = {'success': False, 'error': str(e)}
    
    async def test_data_persistence(self):
        """🧪 Test data persistence"""
        logger.info("🧪 Testing data persistence...")
        
        try:
            # Test saving and loading all data types
            await self.agent._save_strategy_registry()
            await self.agent._save_performance_history()
            await self.agent._save_evolution_history()
            
            # Check if files were created
            files_created = [
                (self.agent.registry_path / "strategy_registry.json").exists(),
                (self.agent.evolution_path / "performance_history.json").exists(),
                (self.agent.evolution_path / "evolution_history.json").exists()
            ]
            
            self.test_results['data_persistence'] = {
                'success': all(files_created),
                'registry_saved': files_created[0],
                'performance_saved': files_created[1],
                'evolution_saved': files_created[2]
            }
            
            logger.info("✅ Data persistence test passed")
            
        except Exception as e:
            logger.error(f"❌ Data persistence test failed: {e}")
            self.test_results['data_persistence'] = {'success': False, 'error': str(e)}
    
    async def print_test_results(self):
        """📊 Print comprehensive test results"""
        logger.info("\n" + "="*80)
        logger.info("🧪 STRATEGY EVOLUTION AGENT TEST RESULTS")
        logger.info("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        
        logger.info(f"📊 Overall Results: {passed_tests}/{total_tests} tests passed")
        logger.info(f"✅ Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        logger.info("")
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            logger.info(f"{status} {test_name.replace('_', ' ').title()}")
            
            if not result.get('success', False) and 'error' in result:
                logger.info(f"    Error: {result['error']}")
            
            # Print additional details
            for key, value in result.items():
                if key not in ['success', 'error']:
                    logger.info(f"    {key}: {value}")
            logger.info("")
        
        logger.info("="*80)
        
        if passed_tests == total_tests:
            logger.info("🎉 All tests passed! Strategy Evolution Agent is ready for use.")
        else:
            logger.info("⚠️ Some tests failed. Please check the configuration and dependencies.")

async def main():
    """🚀 Main test function"""
    tester = StrategyEvolutionAgentTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 Strategy Evolution Agent testing completed successfully!")
        return 0
    else:
        print("\n❌ Strategy Evolution Agent testing failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
