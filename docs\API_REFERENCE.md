# 📚 API REFERENCE - NIFTY & BANK NIFTY OPTIONS TRADING SYSTEM

## 🎯 System Overview

The Options Trading System provides a comprehensive API for NIFTY and BANK NIFTY options trading with multi-timeframe support (1min→3min,5min,15min).

## 🚀 Main Entry Points

### Command Line Interface

```bash
# Individual Agent Execution
python main.py --agent <agent_name> [--config <config_path>] [--demo]

# Workflow Execution  
python main.py --workflow <workflow_name> [--monitor]

# Available Agents
--agent data_ingestion          # Download 1min data, generate multi-timeframes
--agent feature_engineering     # Calculate Greeks, volatility, technical indicators
--agent strategy_generation     # Generate options strategies
--agent backtesting            # Backtest strategies with realistic costs
--agent ai_training            # Train ML models on options data
--agent market_monitoring      # Multi-timeframe market surveillance
--agent signal_generation      # Generate trading signals
--agent risk_management        # Greeks-based risk controls
--agent execution              # Smart options order execution
--agent performance_analysis   # Options-specific performance metrics
--agent llm_interface          # Natural language trading interface
--agent strategy_evolution     # Adaptive strategy optimization

# Available Workflows
--workflow full_pipeline              # Complete end-to-end pipeline
--workflow training_pipeline          # Data + Features + Strategies + Backtesting + AI
--workflow live_trading              # Real-time trading workflow
--workflow data_pipeline             # Data ingestion and feature engineering
--workflow strategy_development      # Strategy generation and optimization
--workflow options_research          # Research and analysis workflow
--workflow multi_timeframe_analysis  # Multi-timeframe analysis workflow
```

## 📊 Agent APIs

### 1. Data Ingestion Agent

```python
from agents.options_data_ingestion_agent import OptionsDataIngestionAgent

# Initialize agent
agent = OptionsDataIngestionAgent()
await agent.initialize()

# Start data ingestion (downloads 1min, generates 3min,5min,15min)
await agent.start()

# Configuration
config = {
    'underlying_symbols': ['NIFTY', 'BANKNIFTY'],
    'historical_days': 30,
    'chunk_size': 100,
    'timeframes': ['1min', '3min', '5min', '15min']
}
```

**Key Methods:**
- `_download_underlying_1min_data()` - Download 1-minute data
- `_generate_multi_timeframes()` - Generate higher timeframes
- `_resample_data()` - Resample data to different timeframes
- `_save_timeframe_data()` - Save data by timeframe

### 2. Feature Engineering Agent

```python
from agents.options_feature_engineering_agent import OptionsFeatureEngineeringAgent

# Initialize agent
agent = OptionsFeatureEngineeringAgent()
await agent.initialize()

# Process features for all timeframes
await agent.start()

# Key features generated:
# - Greeks (Delta, Gamma, Theta, Vega, Rho)
# - Implied Volatility
# - Technical Indicators (RSI, SMA, EMA, Bollinger Bands)
# - Options Flow Indicators
```

**Key Methods:**
- `_process_timeframe_features()` - Process features for specific timeframe
- `_calculate_greeks()` - Calculate options Greeks
- `_calculate_volatility_metrics()` - Calculate volatility features
- `_calculate_technical_features()` - Calculate technical indicators

### 3. Strategy Generation Agent

```python
from agents.options_strategy_generation_agent import OptionsStrategyGenerationAgent

# Initialize agent
agent = OptionsStrategyGenerationAgent()
await agent.initialize()

# Generate strategies
await agent.start()

# Supported strategy types:
# - Directional: Long Call, Long Put, Covered Call
# - Volatility: Long Straddle, Long Strangle, Iron Condor
# - Spreads: Bull Call Spread, Bear Put Spread, Calendar Spread
```

**Key Methods:**
- `_generate_directional_strategies()` - Generate directional strategies
- `_generate_volatility_strategies()` - Generate volatility strategies
- `_generate_spread_strategies()` - Generate spread strategies
- `_optimize_strategy_parameters()` - Optimize strategy parameters

### 4. Market Monitoring Agent

```python
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent

# Initialize agent with multi-timeframe support
agent = OptionsMarketMonitoringAgent()
await agent.initialize()

# Start multi-timeframe monitoring
await agent.start()

# Monitoring intervals by timeframe:
# - 1min: Every 60 seconds
# - 3min: Every 180 seconds  
# - 5min: Every 300 seconds
# - 15min: Every 900 seconds
```

**Key Methods:**
- `_monitor_timeframe_data()` - Monitor specific timeframe
- `_analyze_price_movements()` - Analyze price changes
- `_analyze_volume_patterns()` - Analyze volume spikes
- `_generate_multi_timeframe_alerts()` - Generate cross-timeframe alerts

## 🔧 Utility APIs

### Options Pricing Utilities

```python
from utils.options_pricing import OptionsBlackScholes, OptionsGreeks, ImpliedVolatility

# Black-Scholes pricing
call_price = OptionsBlackScholes.call_price(S=25000, K=25000, T=0.25, r=0.06, sigma=0.20)
put_price = OptionsBlackScholes.put_price(S=25000, K=25000, T=0.25, r=0.06, sigma=0.20)

# Greeks calculation
delta = OptionsGreeks.delta(S=25000, K=25000, T=0.25, r=0.06, sigma=0.20, option_type='call')
gamma = OptionsGreeks.gamma(S=25000, K=25000, T=0.25, r=0.06, sigma=0.20)
theta = OptionsGreeks.theta(S=25000, K=25000, T=0.25, r=0.06, sigma=0.20, option_type='call')
vega = OptionsGreeks.vega(S=25000, K=25000, T=0.25, r=0.06, sigma=0.20)

# Implied volatility
iv = ImpliedVolatility.calculate_iv(market_price=150, S=25000, K=25000, T=0.25, r=0.06, option_type='call')

# Vectorized calculations for multiple options
import numpy as np
S = np.array([25000, 25000, 25000])
K = np.array([24500, 25000, 25500])
T = np.array([0.25, 0.25, 0.25])
sigma = np.array([0.20, 0.20, 0.20])

from utils.options_pricing import vectorized_black_scholes, vectorized_greeks
prices = vectorized_black_scholes(S, K, T, 0.06, sigma, 'call')
greeks = vectorized_greeks(S, K, T, 0.06, sigma, 'call')
```

## 📁 Data Structures

### Options Contract

```python
@dataclass
class OptionsContract:
    symbol: str              # e.g., "NIFTY25000CE"
    underlying: str          # "NIFTY" or "BANKNIFTY"
    strike_price: float      # Strike price
    expiry_date: str         # Expiry date
    option_type: str         # "CE" or "PE"
    lot_size: int           # Contract lot size
```

### Strategy Definition

```python
@dataclass
class OptionsStrategy:
    strategy_id: str
    strategy_type: StrategyType
    underlying: str
    legs: List[StrategyLeg]
    max_loss: float
    max_profit: float
    breakeven_points: List[float]
    margin_required: float
```

### Backtest Results

```python
@dataclass
class BacktestResults:
    strategy_id: str
    total_return: float
    annualized_return: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    greeks_pnl: Dict[str, float]
    volatility_pnl: float
    time_decay_pnl: float
```

## 🔄 Data Flow

### Multi-Timeframe Data Pipeline

```
1. Download 1-minute OHLCV data
   ↓
2. Generate 3min, 5min, 15min timeframes
   ↓
3. Save to data/{historical|live}/{timeframe}/
   ↓
4. Feature engineering for each timeframe
   ↓
5. Save features to data/features/{timeframe}/
   ↓
6. Multi-timeframe analysis and signals
```

### File Structure

```
data/
├── historical/
│   ├── 1min/     # 1-minute historical data
│   ├── 3min/     # 3-minute historical data
│   ├── 5min/     # 5-minute historical data
│   └── 15min/    # 15-minute historical data
├── live/
│   ├── 1min/     # 1-minute live data
│   ├── 3min/     # 3-minute live data
│   ├── 5min/     # 5-minute live data
│   └── 15min/    # 15-minute live data
├── features/
│   ├── 1min/     # 1-minute features
│   ├── 3min/     # 3-minute features
│   ├── 5min/     # 5-minute features
│   └── 15min/    # 15-minute features
├── strategies/   # Generated strategies
├── backtest/     # Backtesting results
├── models/       # Trained ML models
└── greeks/       # Greeks data by timeframe
```

## ⚙️ Configuration

### Environment Variables

```bash
# SmartAPI Configuration
SMARTAPI_API_KEY=your_api_key
SMARTAPI_CLIENT_ID=your_client_id
SMARTAPI_PASSWORD=your_password
SMARTAPI_TOTP_SECRET=your_totp_secret

# Trading Configuration
TRADING_MODE=paper  # paper or live
CAPITAL=100000
RISK_PER_TRADE=0.02
MAX_POSITIONS=5

# Performance Configuration
CUDA_VISIBLE_DEVICES=0
MAX_WORKERS=8
CHUNK_SIZE=5000
COMPRESSION=snappy
```

### Configuration Files

```yaml
# config/environment.yaml
smartapi:
  api_key: "your_api_key"
  client_id: "your_client_id"
  password: "your_password"
  totp_secret: "your_totp_secret"

trading:
  mode: "paper"
  capital: 100000
  risk_per_trade: 0.02
  max_positions: 5

data:
  timeframes: ["1min", "3min", "5min", "15min"]
  storage_path: "data"
  retention_days: 30
  compression: "snappy"
```

## 🚨 Error Handling

### Common Exceptions

```python
# Agent initialization errors
class AgentInitializationError(Exception):
    pass

# Data processing errors
class DataProcessingError(Exception):
    pass

# Strategy generation errors
class StrategyGenerationError(Exception):
    pass

# Backtesting errors
class BacktestingError(Exception):
    pass
```

### Error Recovery

```python
# Automatic retry mechanism
@retry(max_attempts=3, delay=1.0)
async def download_data():
    # Download logic with automatic retry
    pass

# Graceful degradation
try:
    await agent.start()
except Exception as e:
    logger.error(f"Agent failed: {e}")
    await agent.cleanup()
```

## 📊 Performance Monitoring

### Metrics Collection

```python
# Performance metrics
execution_time = time.time() - start_time
memory_usage = process.memory_info().rss / 1024 / 1024  # MB
throughput = records_processed / execution_time

# Logging
logger.info(f"Processed {records_processed} records in {execution_time:.2f}s")
logger.info(f"Memory usage: {memory_usage:.2f} MB")
logger.info(f"Throughput: {throughput:.2f} records/second")
```

## 🔗 Integration Examples

### Custom Strategy Development

```python
# Create custom strategy
custom_strategy = OptionsStrategy(
    strategy_id="custom_iron_condor",
    strategy_type=StrategyType.IRON_CONDOR,
    underlying="NIFTY",
    legs=[
        StrategyLeg(symbol="NIFTY24500PE", quantity=-1, action="SELL"),
        StrategyLeg(symbol="NIFTY24750PE", quantity=1, action="BUY"),
        StrategyLeg(symbol="NIFTY25250CE", quantity=1, action="BUY"),
        StrategyLeg(symbol="NIFTY25500CE", quantity=-1, action="SELL")
    ]
)

# Backtest custom strategy
backtester = OptionsBacktestingAgent()
results = await backtester.backtest_strategy(custom_strategy, historical_data)
```

### Real-time Monitoring Integration

```python
# Set up real-time monitoring
monitor = OptionsMarketMonitoringAgent()
await monitor.initialize()

# Subscribe to alerts
async def handle_alert(alert):
    if alert.type == "volatility_spike":
        logger.warning(f"Volatility spike detected: {alert.message}")
    elif alert.type == "price_movement":
        logger.info(f"Significant price movement: {alert.message}")

monitor.subscribe_alerts(handle_alert)
await monitor.start()
```

---

**📖 For more detailed examples and advanced usage, see:**
- [Strategy Documentation](STRATEGY_DOCUMENTATION.md)
- [Deployment Guide](DEPLOYMENT_GUIDE.md)
- [Quick Start Guide](QUICK_START_GUIDE.md)
