#!/usr/bin/env python3
"""
🧪 Comprehensive Test Suite for Enhanced Options Performance Analysis Agent

Tests all implemented features with sample data to validate functionality.
"""

import asyncio
import logging
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import json
import sys
import os

# Add the agents directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))

from options_performance_analysis_agent import OptionsPerformanceAnalysisAgent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestOptionsPerformanceAgent:
    """Test suite for the Options Performance Analysis Agent"""
    
    def __init__(self):
        self.agent = OptionsPerformanceAnalysisAgent()
        self.test_data_dir = Path("test_data")
        self.test_data_dir.mkdir(exist_ok=True)
        
    async def create_sample_trade_data(self) -> pl.DataFrame:
        """Create comprehensive sample trade data for testing"""
        logger.info("🔧 Creating sample trade data...")
        
        # Generate 100 sample trades
        np.random.seed(42)  # For reproducible results
        n_trades = 100
        
        # Base data
        trade_ids = [f"trade_{i:03d}" for i in range(n_trades)]
        strategy_ids = np.random.choice(["strat_001", "strat_002", "strat_003", "strat_004"], n_trades)
        
        # Time data
        start_date = datetime.now() - timedelta(days=30)
        entry_times = [start_date + timedelta(hours=np.random.randint(0, 720)) for _ in range(n_trades)]
        exit_times = [entry + timedelta(minutes=np.random.randint(5, 300)) for entry in entry_times]
        
        # Price data
        entry_prices = np.random.uniform(100, 500, n_trades)
        exit_prices = entry_prices * (1 + np.random.normal(0, 0.1, n_trades))  # 10% volatility
        quantities = np.random.randint(1, 10, n_trades)
        
        # Options data
        option_types = np.random.choice(["CALL", "PUT"], n_trades)
        trade_types = np.random.choice(["CE", "PE"], n_trades)
        strike_prices = entry_prices * np.random.uniform(0.95, 1.05, n_trades)
        underlying_entry_prices = entry_prices * np.random.uniform(0.98, 1.02, n_trades)
        underlying_exit_prices = underlying_entry_prices * (1 + np.random.normal(0, 0.05, n_trades))
        
        # Expected prices (with some slippage)
        expected_entry_prices = entry_prices + np.random.normal(0, 2, n_trades)
        expected_exit_prices = exit_prices + np.random.normal(0, 2, n_trades)
        
        # Signal data
        signal_confidence = np.random.uniform(0.3, 0.9, n_trades)
        signal_sources = np.random.choice(["AI_Model", "Technical", "Fundamental"], n_trades)
        
        # Exit reasons
        is_target_hit = np.random.choice([True, False], n_trades, p=[0.4, 0.6])
        is_sl_hit = np.random.choice([True, False], n_trades, p=[0.2, 0.8])
        is_manual_exit = ~(is_target_hit | is_sl_hit)
        
        # Market conditions
        market_regimes = np.random.choice(["bullish", "bearish", "sideways"], n_trades)
        volatility_regimes = np.random.choice(["low_iv", "medium_iv", "high_iv"], n_trades)
        news_days = np.random.choice([True, False], n_trades, p=[0.2, 0.8])
        expiry_proximity = np.random.choice(["far", "medium", "near"], n_trades)
        
        # Model predictions
        model_predicted_direction = np.random.choice(["long", "short"], n_trades)
        model_predicted_roi = np.random.normal(0.05, 0.1, n_trades)
        actual_direction = np.random.choice(["long", "short"], n_trades)
        
        # Risk management
        allowed_capital_at_risk = np.random.uniform(1000, 5000, n_trades)
        actual_loss = np.minimum(0, (exit_prices - entry_prices) * quantities)
        daily_drawdown_threshold = np.random.uniform(500, 2000, n_trades)
        trading_paused = np.random.choice([True, False], n_trades, p=[0.1, 0.9])
        risky_signals_filtered = np.random.choice([True, False], n_trades, p=[0.8, 0.2])
        
        # Create DataFrame
        sample_data = {
            "trade_id": trade_ids,
            "strategy_id": strategy_ids,
            "entry_time": entry_times,
            "exit_time": exit_times,
            "entry_price": entry_prices,
            "exit_price": exit_prices,
            "quantity": quantities,
            "trade_type": trade_types,
            "expected_entry_price": expected_entry_prices,
            "expected_exit_price": expected_exit_prices,
            "signal_confidence": signal_confidence,
            "is_target_hit": is_target_hit,
            "is_sl_hit": is_sl_hit,
            "is_manual_exit": is_manual_exit,
            "market_regime": market_regimes,
            "volatility_regime": volatility_regimes,
            "news_day": news_days,
            "expiry_proximity": expiry_proximity,
            "model_predicted_direction": model_predicted_direction,
            "model_predicted_roi": model_predicted_roi,
            "actual_direction": actual_direction,
            "allowed_capital_at_risk": allowed_capital_at_risk,
            "actual_loss": actual_loss,
            "daily_drawdown_threshold": daily_drawdown_threshold,
            "trading_paused": trading_paused,
            "risky_signals_filtered": risky_signals_filtered,
            "signal_source": signal_sources,
            "strike_price": strike_prices,
            "option_type": option_types,
            "underlying_entry_price": underlying_entry_prices,
            "underlying_exit_price": underlying_exit_prices
        }
        
        df = pl.DataFrame(sample_data)
        
        # Save to parquet for agent to load
        trade_data_path = Path("data/trades")
        trade_data_path.mkdir(parents=True, exist_ok=True)
        df.write_parquet(trade_data_path / "completed_trades.parquet")
        
        logger.info(f"✅ Created {n_trades} sample trades and saved to parquet file")
        return df
        
    async def create_sample_greeks_data(self, trade_df: pl.DataFrame) -> pl.DataFrame:
        """Create sample Greeks data for testing"""
        logger.info("🔧 Creating sample Greeks data...")
        
        greeks_records = []
        
        for trade in trade_df.iter_rows(named=True):
            trade_id = trade["trade_id"]
            entry_time = trade["entry_time"]
            exit_time = trade["exit_time"]
            
            # Create Greeks data points during the trade
            time_points = [
                entry_time,
                entry_time + (exit_time - entry_time) / 2,  # Mid-point
                exit_time
            ]
            
            for timestamp in time_points:
                # Simulate realistic Greeks values
                delta = np.random.uniform(0.2, 0.8) if trade["option_type"] == "CALL" else np.random.uniform(-0.8, -0.2)
                gamma = np.random.uniform(0.01, 0.05)
                theta = np.random.uniform(-0.1, -0.01)  # Always negative
                vega = np.random.uniform(0.1, 0.3)
                rho = np.random.uniform(0.01, 0.05) if trade["option_type"] == "CALL" else np.random.uniform(-0.05, -0.01)
                
                greeks_records.append({
                    "trade_id": trade_id,
                    "timestamp": timestamp,
                    "delta": delta,
                    "gamma": gamma,
                    "theta": theta,
                    "vega": vega,
                    "rho": rho,
                    "implied_volatility": np.random.uniform(0.15, 0.35),
                    "time_to_expiry": np.random.uniform(1, 30),  # Days
                    "underlying_price": trade["underlying_entry_price"],
                    "strike_price": trade["strike_price"],
                    "option_price": trade["entry_price"]
                })
        
        greeks_df = pl.DataFrame(greeks_records)
        
        # Save to parquet
        greeks_data_path = Path("data/greeks")
        greeks_data_path.mkdir(parents=True, exist_ok=True)
        greeks_df.write_parquet(greeks_data_path / "options_greeks.parquet")
        
        logger.info(f"✅ Created {len(greeks_records)} Greeks records and saved to parquet file")
        return greeks_df
        
    async def create_sample_historical_data(self) -> pl.DataFrame:
        """Create sample historical underlying price data"""
        logger.info("🔧 Creating sample historical data...")
        
        # Generate 30 days of minute-by-minute data
        start_date = datetime.now() - timedelta(days=30)
        timestamps = []
        prices = []
        
        current_time = start_date
        current_price = 1000.0  # Starting price
        
        while current_time < datetime.now():
            timestamps.append(current_time)
            # Random walk with slight upward bias
            price_change = np.random.normal(0.001, 0.01)  # 0.1% mean, 1% std
            current_price *= (1 + price_change)
            prices.append(current_price)
            current_time += timedelta(minutes=1)
        
        historical_df = pl.DataFrame({
            "timestamp": timestamps,
            "close": prices
        })
        
        # Save to parquet
        historical_data_path = Path("data/historical")
        historical_data_path.mkdir(parents=True, exist_ok=True)
        historical_df.write_parquet(historical_data_path / "underlying_data.parquet")
        
        logger.info(f"✅ Created {len(timestamps)} historical price points and saved to parquet file")
        return historical_df
        
    async def run_comprehensive_tests(self):
        """Run comprehensive tests of all agent features"""
        logger.info("🚀 Starting comprehensive test suite...")
        
        try:
            # Create test data
            trade_df = await self.create_sample_trade_data()
            greeks_df = await self.create_sample_greeks_data(trade_df)
            historical_df = await self.create_sample_historical_data()
            
            # Initialize and start the agent
            await self.agent.initialize()
            
            logger.info("🧪 Testing agent with sample data...")
            success = await self.agent.start()
            
            if success:
                logger.info("✅ All tests completed successfully!")
                
                # Test natural language queries
                logger.info("🗣️ Testing natural language queries...")
                test_queries = [
                    "What is my win rate?",
                    "What is my total PnL?",
                    "Which is my best strategy?",
                    "What is my worst trade?",
                    "How much did theta contribute to my PnL?",
                    "What is my maximum drawdown?"
                ]
                
                # Load the evaluated trades for queries
                evaluated_trades = await self.agent._load_trade_data()
                if evaluated_trades is not None and not evaluated_trades.is_empty():
                    evaluated_trades = await self.agent._evaluate_trade_level_performance(evaluated_trades)
                    
                    for query in test_queries:
                        response = await self.agent._natural_language_query(query, evaluated_trades)
                        logger.info(f"   Q: {query}")
                        logger.info(f"   A: {response}")
                        logger.info("")
                
                logger.info("🎉 Test suite completed successfully!")
                return True
            else:
                logger.error("❌ Agent failed to start properly")
                return False
                
        except Exception as e:
            logger.error(f"❌ Test suite failed: {e}")
            return False
        finally:
            await self.agent.cleanup()

async def main():
    """Main test function"""
    logger.info("🧪 Options Performance Analysis Agent - Test Suite")
    logger.info("=" * 60)
    
    tester = TestOptionsPerformanceAgent()
    success = await tester.run_comprehensive_tests()
    
    if success:
        logger.info("🎉 All tests passed! The agent is ready for production use.")
    else:
        logger.error("❌ Some tests failed. Please check the logs for details.")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
