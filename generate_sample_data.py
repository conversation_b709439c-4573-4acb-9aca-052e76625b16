#!/usr/bin/env python3
"""
Generate Sample Options Data for Feature Engineering and AI Training
Creates realistic NIFTY & BANK NIFTY options data for the specified date range
"""

import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta
from pathlib import Path
import random
import math

def generate_sample_options_data():
    """Generate sample options data for NIFTY and BANK NIFTY"""
    
    print("🚀 Generating sample options data for feature engineering...")
    
    # Date range: 01/07/2024 to 17/07/2024 (using 2024 instead of 2025)
    start_date = datetime(2024, 7, 1)
    end_date = datetime(2024, 7, 17)
    
    # Data paths
    data_path = Path("data")
    historical_path = data_path / "historical"
    
    # Timeframes to generate
    timeframes = ["1min", "3min", "5min", "15min"]
    
    # Underlying symbols with their base prices
    underlyings = {
        "NIFTY": {"base_price": 24500, "strike_interval": 50, "lot_size": 25},
        "BANKNIFTY": {"base_price": 52000, "strike_interval": 100, "lot_size": 15}
    }
    
    # Generate data for each underlying
    for underlying, params in underlyings.items():
        print(f"📊 Generating data for {underlying}...")
        
        # Generate 1-minute data first
        one_min_data = generate_1min_data(underlying, params, start_date, end_date)
        
        if one_min_data is not None:
            # Save 1-minute data
            save_data(one_min_data, underlying, "1min", historical_path)
            
            # Generate other timeframes from 1-minute data
            for timeframe in ["3min", "5min", "15min"]:
                resampled_data = resample_data(one_min_data, timeframe)
                if resampled_data is not None:
                    save_data(resampled_data, underlying, timeframe, historical_path)
    
    print("✅ Sample options data generation completed!")

def generate_1min_data(underlying: str, params: dict, start_date: datetime, end_date: datetime):
    """Generate 1-minute options data"""
    
    base_price = params["base_price"]
    strike_interval = params["strike_interval"]
    lot_size = params["lot_size"]
    
    # Generate strikes around base price
    strikes = []
    for i in range(-5, 6):  # 11 strikes
        strike = base_price + (i * strike_interval)
        strikes.append(strike)
    
    # Expiry dates
    expiries = ["24JUL2024", "31JUL2024", "07AUG2024"]
    
    data_rows = []
    current_date = start_date
    
    while current_date <= end_date:
        # Skip weekends
        if current_date.weekday() >= 5:
            current_date += timedelta(days=1)
            continue
        
        # Generate data for trading hours (9:15 AM to 3:30 PM)
        market_start = current_date.replace(hour=9, minute=15, second=0, microsecond=0)
        market_end = current_date.replace(hour=15, minute=30, second=0, microsecond=0)
        
        current_time = market_start
        while current_time <= market_end:
            # Simulate underlying price movement
            underlying_price = base_price + random.gauss(0, base_price * 0.02)
            
            for expiry in expiries:
                for strike in strikes:
                    for option_type in ["CE", "PE"]:
                        # Calculate realistic option price based on moneyness
                        moneyness = underlying_price / strike
                        time_to_expiry = 30 / 365  # Approximate days to expiry
                        
                        # Simple Black-Scholes approximation for realistic prices
                        if option_type == "CE":
                            intrinsic = max(0, underlying_price - strike)
                            time_value = max(10, strike * 0.1 * math.sqrt(time_to_expiry) * random.uniform(0.8, 1.2))
                        else:  # PE
                            intrinsic = max(0, strike - underlying_price)
                            time_value = max(10, strike * 0.1 * math.sqrt(time_to_expiry) * random.uniform(0.8, 1.2))
                        
                        option_price = intrinsic + time_value
                        
                        # Add some noise for OHLC
                        noise = option_price * 0.02
                        open_price = option_price + random.gauss(0, noise)
                        high_price = option_price + abs(random.gauss(0, noise))
                        low_price = option_price - abs(random.gauss(0, noise))
                        close_price = option_price + random.gauss(0, noise)
                        
                        # Ensure OHLC relationships
                        high_price = max(high_price, open_price, close_price)
                        low_price = min(low_price, open_price, close_price)
                        
                        symbol = f"{underlying}{expiry}{int(strike)}{option_type}"
                        
                        data_row = {
                            'timestamp': current_time,
                            'symbol': symbol,
                            'underlying': underlying,
                            'strike_price': strike,
                            'expiry_date': expiry,
                            'option_type': option_type,
                            'open': round(open_price, 2),
                            'high': round(high_price, 2),
                            'low': round(low_price, 2),
                            'close': round(close_price, 2),
                            'volume': random.randint(100, 2000),
                            'open_interest': random.randint(1000, 50000),
                            'underlying_price': round(underlying_price, 2)
                        }
                        data_rows.append(data_row)
            
            current_time += timedelta(minutes=1)
        
        current_date += timedelta(days=1)
    
    if data_rows:
        return pl.DataFrame(data_rows)
    else:
        return None

def resample_data(data: pl.DataFrame, timeframe: str):
    """Resample 1-minute data to specified timeframe"""
    
    # Extract minutes from timeframe
    minutes_map = {"3min": 3, "5min": 5, "15min": 15}
    minutes = minutes_map.get(timeframe, 1)
    
    try:
        resampled_data = (
            data
            .with_columns([
                pl.col("timestamp").dt.truncate(f"{minutes}m").alias("timeframe_start")
            ])
            .group_by(["symbol", "underlying", "strike_price", "expiry_date", "option_type", "timeframe_start"])
            .agg([
                pl.col("open").first().alias("open"),
                pl.col("high").max().alias("high"),
                pl.col("low").min().alias("low"),
                pl.col("close").last().alias("close"),
                pl.col("volume").sum().alias("volume"),
                pl.col("open_interest").last().alias("open_interest"),
                pl.col("underlying_price").last().alias("underlying_price")
            ])
            .rename({"timeframe_start": "timestamp"})
            .sort(["symbol", "timestamp"])
        )
        
        return resampled_data
        
    except Exception as e:
        print(f"❌ Error resampling to {timeframe}: {e}")
        return None

def save_data(data: pl.DataFrame, underlying: str, timeframe: str, base_path: Path):
    """Save data to parquet file"""
    
    try:
        # Create filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{underlying}_{timeframe}_{timestamp}.parquet"
        filepath = base_path / timeframe / filename
        
        # Save with compression
        data.write_parquet(filepath, compression="snappy")
        
        print(f"💾 Saved {data.height} records to {timeframe}/{filename}")
        
    except Exception as e:
        print(f"❌ Error saving {timeframe} data for {underlying}: {e}")

if __name__ == "__main__":
    generate_sample_options_data()
    print("\n🎯 Sample data ready for feature engineering and AI training!")
