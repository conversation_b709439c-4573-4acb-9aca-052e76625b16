# 📈 STRATEGY DOCUMENTATION - NIFTY & BANK NIFTY OPTIONS

## 🎯 Overview

This document covers all options trading strategies implemented in the system, specifically designed for NIFTY and BANK NIFTY options with multi-timeframe analysis support.

## 📊 Strategy Categories

### 1. Directional Strategies

#### Long Call
**Market View:** Bullish  
**Max Loss:** Premium paid  
**Max Profit:** Unlimited  
**Breakeven:** Strike + Premium  

```python
# Example: NIFTY Long Call
{
    "strategy_type": "long_call",
    "underlying": "NIFTY",
    "legs": [
        {
            "symbol": "NIFTY25000CE",
            "option_type": "CE",
            "strike_price": 25000,
            "quantity": 1,
            "action": "BUY"
        }
    ]
}
```

**When to Use:**
- Strong bullish view on underlying
- Low volatility environment
- Expecting significant upward movement

#### Long Put
**Market View:** Bearish  
**Max Loss:** Premium paid  
**Max Profit:** Strike - Premium  
**Breakeven:** Strike - Premium  

```python
# Example: BANK NIFTY Long Put
{
    "strategy_type": "long_put",
    "underlying": "BANKNIFTY",
    "legs": [
        {
            "symbol": "BANKNIFTY48000PE",
            "option_type": "PE",
            "strike_price": 48000,
            "quantity": 1,
            "action": "BUY"
        }
    ]
}
```

#### Covered Call
**Market View:** Neutral to slightly bullish  
**Max Loss:** Unlimited (on underlying)  
**Max Profit:** Strike - Stock Price + Premium  
**Breakeven:** Stock Price - Premium  

### 2. Volatility Strategies

#### Long Straddle
**Market View:** High volatility expected  
**Max Loss:** Total premium paid  
**Max Profit:** Unlimited  
**Breakeven:** Strike ± Total Premium  

```python
# Example: NIFTY Long Straddle
{
    "strategy_type": "long_straddle",
    "underlying": "NIFTY",
    "legs": [
        {
            "symbol": "NIFTY25000CE",
            "option_type": "CE",
            "strike_price": 25000,
            "quantity": 1,
            "action": "BUY"
        },
        {
            "symbol": "NIFTY25000PE",
            "option_type": "PE",
            "strike_price": 25000,
            "quantity": 1,
            "action": "BUY"
        }
    ]
}
```

**When to Use:**
- Expecting high volatility
- Uncertain about direction
- Before earnings or major events

#### Long Strangle
**Market View:** High volatility, wider range  
**Max Loss:** Total premium paid  
**Max Profit:** Unlimited  
**Breakeven:** Lower Strike - Premium, Upper Strike + Premium  

```python
# Example: NIFTY Long Strangle
{
    "strategy_type": "long_strangle",
    "underlying": "NIFTY",
    "legs": [
        {
            "symbol": "NIFTY24750PE",
            "option_type": "PE",
            "strike_price": 24750,
            "quantity": 1,
            "action": "BUY"
        },
        {
            "symbol": "NIFTY25250CE",
            "option_type": "CE",
            "strike_price": 25250,
            "quantity": 1,
            "action": "BUY"
        }
    ]
}
```

#### Iron Condor
**Market View:** Low volatility, range-bound  
**Max Loss:** Difference in strikes - Net premium  
**Max Profit:** Net premium received  
**Breakeven:** Multiple breakeven points  

```python
# Example: NIFTY Iron Condor
{
    "strategy_type": "iron_condor",
    "underlying": "NIFTY",
    "legs": [
        {
            "symbol": "NIFTY24500PE",
            "option_type": "PE",
            "strike_price": 24500,
            "quantity": 1,
            "action": "SELL"
        },
        {
            "symbol": "NIFTY24750PE",
            "option_type": "PE",
            "strike_price": 24750,
            "quantity": 1,
            "action": "BUY"
        },
        {
            "symbol": "NIFTY25250CE",
            "option_type": "CE",
            "strike_price": 25250,
            "quantity": 1,
            "action": "BUY"
        },
        {
            "symbol": "NIFTY25500CE",
            "option_type": "CE",
            "strike_price": 25500,
            "quantity": 1,
            "action": "SELL"
        }
    ]
}
```

### 3. Spread Strategies

#### Bull Call Spread
**Market View:** Moderately bullish  
**Max Loss:** Net premium paid  
**Max Profit:** Difference in strikes - Net premium  
**Breakeven:** Lower strike + Net premium  

```python
# Example: NIFTY Bull Call Spread
{
    "strategy_type": "bull_call_spread",
    "underlying": "NIFTY",
    "legs": [
        {
            "symbol": "NIFTY25000CE",
            "option_type": "CE",
            "strike_price": 25000,
            "quantity": 1,
            "action": "BUY"
        },
        {
            "symbol": "NIFTY25250CE",
            "option_type": "CE",
            "strike_price": 25250,
            "quantity": 1,
            "action": "SELL"
        }
    ]
}
```

#### Bear Put Spread
**Market View:** Moderately bearish  
**Max Loss:** Net premium paid  
**Max Profit:** Difference in strikes - Net premium  
**Breakeven:** Higher strike - Net premium  

#### Calendar Spread
**Market View:** Low volatility, time decay benefit  
**Max Loss:** Net premium paid  
**Max Profit:** Variable  
**Breakeven:** Near the short strike  

## 🔄 Multi-Timeframe Strategy Selection

### Timeframe-Based Strategy Rules

#### 1-Minute Timeframe
- **Scalping strategies**
- Quick directional plays
- High-frequency volatility strategies
- Tight stop-losses

#### 3-Minute Timeframe
- **Short-term momentum**
- Breakout strategies
- Quick mean reversion

#### 5-Minute Timeframe
- **Intraday trends**
- Support/resistance plays
- Volatility expansion/contraction

#### 15-Minute Timeframe
- **Swing trading**
- Longer-term directional views
- Complex multi-leg strategies

### Strategy Selection Algorithm

```python
def select_strategy_by_timeframe(timeframe, market_conditions):
    """
    Select optimal strategy based on timeframe and market conditions
    """
    if timeframe == "1min":
        if market_conditions["volatility"] > 0.25:
            return "long_straddle"
        elif market_conditions["trend_strength"] > 0.7:
            return "long_call" if market_conditions["direction"] == "bullish" else "long_put"
    
    elif timeframe == "5min":
        if market_conditions["volatility"] < 0.15:
            return "iron_condor"
        elif market_conditions["trend_strength"] > 0.6:
            return "bull_call_spread" if market_conditions["direction"] == "bullish" else "bear_put_spread"
    
    elif timeframe == "15min":
        if market_conditions["volatility"] > 0.20:
            return "long_strangle"
        else:
            return "calendar_spread"
    
    return "long_call"  # Default strategy
```

## 📊 Strategy Performance Metrics

### Key Performance Indicators

#### Risk-Adjusted Returns
- **Sharpe Ratio**: (Return - Risk-free rate) / Standard deviation
- **Sortino Ratio**: (Return - Risk-free rate) / Downside deviation
- **Calmar Ratio**: Annual return / Maximum drawdown

#### Options-Specific Metrics
- **Greeks P&L Attribution**
  - Delta P&L: Price movement impact
  - Gamma P&L: Delta hedging impact
  - Theta P&L: Time decay impact
  - Vega P&L: Volatility change impact

#### Strategy Efficiency
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross profit / Gross loss
- **Average Trade Return**: Mean return per trade
- **Maximum Drawdown**: Largest peak-to-trough decline

### Performance Analysis Example

```python
# Strategy performance analysis
strategy_performance = {
    "strategy_id": "nifty_iron_condor_001",
    "total_return": 0.15,  # 15% return
    "sharpe_ratio": 1.8,
    "sortino_ratio": 2.1,
    "max_drawdown": 0.08,  # 8% max drawdown
    "win_rate": 0.72,      # 72% win rate
    "profit_factor": 2.3,
    "total_trades": 45,
    "avg_trade_return": 0.033,
    "greeks_pnl": {
        "delta": 2500,     # Delta P&L
        "gamma": -800,     # Gamma P&L
        "theta": 4200,     # Theta P&L (time decay benefit)
        "vega": -1100      # Vega P&L (volatility impact)
    },
    "volatility_pnl": -1100,
    "time_decay_pnl": 4200
}
```

## 🎯 Strategy Optimization

### Parameter Optimization

#### Strike Selection
- **ATM (At-the-Money)**: Strike ≈ Spot price
- **ITM (In-the-Money)**: Strike < Spot (calls), Strike > Spot (puts)
- **OTM (Out-of-the-Money)**: Strike > Spot (calls), Strike < Spot (puts)

#### Expiry Selection
- **Weekly expiries**: Higher theta decay, more responsive to price moves
- **Monthly expiries**: Lower theta decay, more time for strategy to work

#### Position Sizing
```python
def calculate_position_size(strategy, account_balance, risk_per_trade):
    """
    Calculate optimal position size based on risk management
    """
    max_loss = strategy["max_loss"]
    risk_amount = account_balance * risk_per_trade
    
    if max_loss > 0:
        position_size = int(risk_amount / max_loss)
    else:
        position_size = 1
    
    return max(1, position_size)
```

### Dynamic Strategy Adjustment

#### Volatility-Based Adjustments
```python
def adjust_strategy_for_volatility(base_strategy, current_iv, historical_iv):
    """
    Adjust strategy based on implied volatility levels
    """
    iv_percentile = current_iv / historical_iv
    
    if iv_percentile > 1.2:  # High IV environment
        # Favor volatility selling strategies
        return modify_strategy_for_high_iv(base_strategy)
    elif iv_percentile < 0.8:  # Low IV environment
        # Favor volatility buying strategies
        return modify_strategy_for_low_iv(base_strategy)
    else:
        return base_strategy
```

## 🛡️ Risk Management

### Position-Level Risk Controls

#### Greeks Limits
```python
risk_limits = {
    "max_delta_exposure": 1000,    # Maximum delta exposure
    "max_gamma_exposure": 100,     # Maximum gamma exposure
    "max_theta_exposure": -500,    # Maximum theta exposure (negative)
    "max_vega_exposure": 2000,     # Maximum vega exposure
    "max_position_size": 0.05      # 5% of portfolio per position
}
```

#### Portfolio-Level Risk Controls
```python
portfolio_limits = {
    "max_portfolio_delta": 2000,   # Portfolio delta limit
    "max_portfolio_gamma": 200,    # Portfolio gamma limit
    "max_drawdown": 0.10,          # 10% maximum drawdown
    "max_correlation": 0.7,        # Maximum strategy correlation
    "max_concentration": 0.20      # 20% maximum in single underlying
}
```

### Exit Rules

#### Profit Taking
- **50% of maximum profit**: Take profits at 50% of max potential
- **Time-based exits**: Close positions at 50% of time to expiry
- **Volatility-based exits**: Close when IV drops significantly

#### Stop Losses
- **2x premium**: Stop loss at 200% of premium paid
- **Greeks-based stops**: Stop when delta exposure exceeds limits
- **Time-based stops**: Close losing positions before expiry

## 📈 Strategy Examples by Market Condition

### Trending Markets
1. **Strong Uptrend**: Long calls, bull call spreads
2. **Strong Downtrend**: Long puts, bear put spreads
3. **Weak Trend**: Covered calls, protective puts

### Range-Bound Markets
1. **Low Volatility**: Iron condors, iron butterflies
2. **High Volatility**: Short straddles, short strangles
3. **Neutral**: Calendar spreads, diagonal spreads

### High Volatility Markets
1. **Volatility Expansion**: Long straddles, long strangles
2. **Volatility Contraction**: Short straddles, short strangles
3. **Uncertain Direction**: Long straddles with adjustments

## 🔄 Strategy Evolution

### Genetic Algorithm Optimization
```python
# Strategy evolution parameters
evolution_config = {
    "population_size": 50,
    "generations": 100,
    "mutation_rate": 0.1,
    "crossover_rate": 0.8,
    "selection_pressure": 0.3,
    "fitness_function": "sharpe_ratio"
}
```

### Reinforcement Learning
- **State**: Market conditions, portfolio state, time to expiry
- **Actions**: Strategy selection, position sizing, exit timing
- **Rewards**: Risk-adjusted returns, Greeks P&L attribution
- **Learning**: Continuous adaptation to market conditions

---

**📊 For implementation details and code examples, see:**
- [API Reference](API_REFERENCE.md)
- [Quick Start Guide](QUICK_START_GUIDE.md)
- [Deployment Guide](DEPLOYMENT_GUIDE.md)
