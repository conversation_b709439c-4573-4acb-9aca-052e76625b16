# 🚀 DEPLOYMENT GUIDE - NIFTY & BANK NIFTY OPTIONS TRADING SYSTEM

## 🎯 Overview

This guide covers production deployment of the options trading system with multi-timeframe support for NIFTY and BANK NIFTY options.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Layer    │    │  Processing     │    │   Application   │
│                 │    │     Layer       │    │     Layer       │
│ • SmartAPI      │───▶│ • 12 Agents     │───▶│ • Main.py       │
│ • Historical    │    │ • Multi-TF      │    │ • Workflows     │
│ • Live Data     │    │ • ML Models     │    │ • Monitoring    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Storage       │    │   Compute       │    │   Interface     │
│                 │    │                 │    │                 │
│ • Parquet Files │    │ • GPU/CPU       │    │ • CLI           │
│ • Multi-TF Data │    │ • Polars/Arrow  │    │ • LLM Interface │
│ • Features      │    │ • Async Proc    │    │ • Monitoring    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🖥️ System Requirements

### Minimum Requirements
- **CPU**: 4 cores, 2.5GHz
- **RAM**: 8GB
- **Storage**: 50GB SSD
- **OS**: Ubuntu 20.04+ / Windows 10+ / macOS 11+
- **Python**: 3.9+

### Recommended Requirements
- **CPU**: 8 cores, 3.0GHz
- **RAM**: 16GB
- **Storage**: 100GB NVMe SSD
- **GPU**: NVIDIA GTX 1660+ (optional)
- **Network**: Stable internet connection

### Production Requirements
- **CPU**: 16 cores, 3.5GHz
- **RAM**: 32GB
- **Storage**: 500GB NVMe SSD
- **GPU**: NVIDIA RTX 3070+ with 8GB VRAM
- **Network**: Dedicated internet line

## 📦 Installation

### 1. System Preparation

#### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install system dependencies
sudo apt install -y python3.11 python3.11-venv python3.11-dev
sudo apt install -y build-essential cmake git curl wget
sudo apt install -y libhdf5-dev libssl-dev libffi-dev

# Install CUDA (optional, for GPU acceleration)
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-ubuntu2004.pin
sudo mv cuda-ubuntu2004.pin /etc/apt/preferences.d/cuda-repository-pin-600
wget https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda-repo-ubuntu2004-11-8-local_11.8.0-520.61.05-1_amd64.deb
sudo dpkg -i cuda-repo-ubuntu2004-11-8-local_11.8.0-520.61.05-1_amd64.deb
sudo cp /var/cuda-repo-ubuntu2004-11-8-local/cuda-*-keyring.gpg /usr/share/keyrings/
sudo apt-get update
sudo apt-get -y install cuda
```

#### Windows
```powershell
# Install Python 3.11
winget install Python.Python.3.11

# Install Git
winget install Git.Git

# Install Visual Studio Build Tools
winget install Microsoft.VisualStudio.2022.BuildTools

# Install CUDA (optional)
# Download and install from NVIDIA website
```

### 2. Project Setup

```bash
# Clone repository
git clone <repository_url>
cd Option

# Create virtual environment
python3.11 -m venv venv

# Activate virtual environment
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# Upgrade pip
pip install --upgrade pip setuptools wheel

# Install dependencies
pip install -r requirements.txt

# Install additional dependencies for production
pip install gunicorn supervisor redis celery
```

### 3. Configuration

#### Environment Configuration
```bash
# Copy environment template
cp config/environment.yaml.example config/environment.yaml

# Edit configuration
nano config/environment.yaml
```

```yaml
# config/environment.yaml
smartapi:
  api_key: "${SMARTAPI_API_KEY}"
  client_id: "${SMARTAPI_CLIENT_ID}"
  password: "${SMARTAPI_PASSWORD}"
  totp_secret: "${SMARTAPI_TOTP_SECRET}"

trading:
  mode: "live"  # paper or live
  capital: 500000
  risk_per_trade: 0.02
  max_positions: 10

data:
  timeframes: ["1min", "3min", "5min", "15min"]
  storage_path: "/data/options"
  retention_days: 90
  compression: "snappy"

performance:
  max_workers: 8
  chunk_size: 10000
  gpu_enabled: true
  memory_limit: "16GB"
```

#### System Environment Variables
```bash
# Create .env file
cat > .env << EOF
# SmartAPI Credentials
SMARTAPI_API_KEY=your_api_key_here
SMARTAPI_CLIENT_ID=your_client_id_here
SMARTAPI_PASSWORD=your_password_here
SMARTAPI_TOTP_SECRET=your_totp_secret_here

# Trading Configuration
TRADING_MODE=live
CAPITAL=500000
RISK_PER_TRADE=0.02

# Performance Configuration
CUDA_VISIBLE_DEVICES=0
MAX_WORKERS=8
CHUNK_SIZE=10000
MEMORY_MONITORING=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_PATH=/var/log/options
EOF

# Load environment variables
source .env
```

## 🔧 Production Configuration

### 1. Directory Structure

```bash
# Create production directories
sudo mkdir -p /opt/options-trading
sudo mkdir -p /data/options/{historical,live,features,strategies,backtest,models}
sudo mkdir -p /var/log/options
sudo mkdir -p /etc/options

# Set permissions
sudo chown -R $USER:$USER /opt/options-trading
sudo chown -R $USER:$USER /data/options
sudo chown -R $USER:$USER /var/log/options

# Copy application
cp -r . /opt/options-trading/
```

### 2. Systemd Services

#### Main Application Service
```bash
# Create systemd service
sudo tee /etc/systemd/system/options-trading.service << EOF
[Unit]
Description=Options Trading System
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/options-trading
Environment=PATH=/opt/options-trading/venv/bin
ExecStart=/opt/options-trading/venv/bin/python main.py --workflow live_trading --monitor
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
```

#### Data Ingestion Service
```bash
# Create data ingestion service
sudo tee /etc/systemd/system/options-data-ingestion.service << EOF
[Unit]
Description=Options Data Ingestion Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/options-trading
Environment=PATH=/opt/options-trading/venv/bin
ExecStart=/opt/options-trading/venv/bin/python main.py --agent data_ingestion
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
```

#### Market Monitoring Service
```bash
# Create market monitoring service
sudo tee /etc/systemd/system/options-monitoring.service << EOF
[Unit]
Description=Options Market Monitoring Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/options-trading
Environment=PATH=/opt/options-trading/venv/bin
ExecStart=/opt/options-trading/venv/bin/python main.py --agent market_monitoring
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
```

### 3. Enable and Start Services

```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable services
sudo systemctl enable options-trading.service
sudo systemctl enable options-data-ingestion.service
sudo systemctl enable options-monitoring.service

# Start services
sudo systemctl start options-trading.service
sudo systemctl start options-data-ingestion.service
sudo systemctl start options-monitoring.service

# Check status
sudo systemctl status options-trading.service
sudo systemctl status options-data-ingestion.service
sudo systemctl status options-monitoring.service
```

## 📊 Monitoring and Logging

### 1. Log Configuration

```bash
# Create log rotation configuration
sudo tee /etc/logrotate.d/options-trading << EOF
/var/log/options/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        systemctl reload options-trading.service
    endscript
}
EOF
```

### 2. Performance Monitoring

#### System Monitoring Script
```bash
#!/bin/bash
# /opt/options-trading/scripts/monitor.sh

# Check system resources
echo "=== System Resources ==="
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)"
echo "Memory Usage: $(free -m | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
echo "Disk Usage: $(df -h /data/options | awk 'NR==2{print $5}')"

# Check service status
echo "=== Service Status ==="
systemctl is-active options-trading.service
systemctl is-active options-data-ingestion.service
systemctl is-active options-monitoring.service

# Check data freshness
echo "=== Data Freshness ==="
find /data/options/live/1min -name "*.parquet" -mmin -5 | wc -l
```

### 3. Health Checks

```python
# /opt/options-trading/scripts/health_check.py
#!/usr/bin/env python3

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime, timedelta

async def check_data_freshness():
    """Check if data is fresh"""
    live_path = Path("/data/options/live/1min")
    cutoff_time = datetime.now() - timedelta(minutes=10)
    
    recent_files = [
        f for f in live_path.glob("*.parquet")
        if datetime.fromtimestamp(f.stat().st_mtime) > cutoff_time
    ]
    
    return len(recent_files) > 0

async def check_agent_health():
    """Check if agents are running"""
    # Implementation to check agent health
    return True

async def main():
    """Main health check"""
    checks = [
        ("Data Freshness", check_data_freshness()),
        ("Agent Health", check_agent_health())
    ]
    
    all_healthy = True
    for name, check in checks:
        try:
            result = await check
            status = "PASS" if result else "FAIL"
            print(f"{name}: {status}")
            if not result:
                all_healthy = False
        except Exception as e:
            print(f"{name}: ERROR - {e}")
            all_healthy = False
    
    sys.exit(0 if all_healthy else 1)

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔒 Security Configuration

### 1. API Security

```bash
# Secure API credentials
sudo chmod 600 /opt/options-trading/config/environment.yaml
sudo chown $USER:$USER /opt/options-trading/config/environment.yaml

# Use environment variables for sensitive data
export SMARTAPI_API_KEY="$(cat /etc/options/api_key)"
export SMARTAPI_CLIENT_ID="$(cat /etc/options/client_id)"
export SMARTAPI_PASSWORD="$(cat /etc/options/password)"
export SMARTAPI_TOTP_SECRET="$(cat /etc/options/totp_secret)"
```

### 2. Network Security

```bash
# Configure firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow from 192.168.1.0/24 to any port 8080  # Internal monitoring
sudo ufw deny 8080  # Block external access to monitoring
```

### 3. Data Encryption

```bash
# Encrypt sensitive data at rest
sudo apt install ecryptfs-utils
sudo mount -t ecryptfs /data/options /data/options
```

## 📈 Performance Optimization

### 1. GPU Configuration

```bash
# Check GPU availability
nvidia-smi

# Set CUDA environment
export CUDA_VISIBLE_DEVICES=0
export CUDA_DEVICE_ORDER=PCI_BUS_ID

# Optimize GPU memory
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

### 2. Memory Optimization

```bash
# Configure swap
sudo fallocate -l 8G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Add to /etc/fstab
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab

# Optimize memory settings
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
```

### 3. Storage Optimization

```bash
# Use SSD for data storage
sudo mount -o noatime,nodiratime /dev/nvme0n1 /data/options

# Configure I/O scheduler
echo mq-deadline | sudo tee /sys/block/nvme0n1/queue/scheduler
```

## 🚨 Backup and Recovery

### 1. Data Backup

```bash
#!/bin/bash
# /opt/options-trading/scripts/backup.sh

BACKUP_DIR="/backup/options/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup data
rsync -av --compress /data/options/ $BACKUP_DIR/data/

# Backup configuration
rsync -av /opt/options-trading/config/ $BACKUP_DIR/config/

# Backup models
rsync -av /data/options/models/ $BACKUP_DIR/models/

# Compress backup
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR

# Keep only last 7 days
find /backup/options -name "*.tar.gz" -mtime +7 -delete
```

### 2. Automated Backup

```bash
# Add to crontab
crontab -e

# Backup every 6 hours
0 */6 * * * /opt/options-trading/scripts/backup.sh

# Health check every 5 minutes
*/5 * * * * /opt/options-trading/venv/bin/python /opt/options-trading/scripts/health_check.py
```

## 🔄 Deployment Automation

### 1. Deployment Script

```bash
#!/bin/bash
# /opt/options-trading/scripts/deploy.sh

set -e

echo "Starting deployment..."

# Stop services
sudo systemctl stop options-trading.service
sudo systemctl stop options-data-ingestion.service
sudo systemctl stop options-monitoring.service

# Backup current version
cp -r /opt/options-trading /opt/options-trading.backup.$(date +%Y%m%d_%H%M%S)

# Update code
cd /opt/options-trading
git pull origin main

# Update dependencies
source venv/bin/activate
pip install -r requirements.txt

# Run tests
python -m pytest tests/ -v

# Start services
sudo systemctl start options-trading.service
sudo systemctl start options-data-ingestion.service
sudo systemctl start options-monitoring.service

# Verify deployment
sleep 30
sudo systemctl status options-trading.service

echo "Deployment completed successfully!"
```

### 2. Blue-Green Deployment

```bash
#!/bin/bash
# Blue-green deployment script

BLUE_DIR="/opt/options-trading-blue"
GREEN_DIR="/opt/options-trading-green"
CURRENT_LINK="/opt/options-trading"

# Determine current and new environments
if [ -L $CURRENT_LINK ]; then
    CURRENT=$(readlink $CURRENT_LINK)
    if [ "$CURRENT" = "$BLUE_DIR" ]; then
        NEW_ENV="green"
        NEW_DIR=$GREEN_DIR
    else
        NEW_ENV="blue"
        NEW_DIR=$BLUE_DIR
    fi
else
    NEW_ENV="blue"
    NEW_DIR=$BLUE_DIR
fi

echo "Deploying to $NEW_ENV environment..."

# Deploy to new environment
rsync -av --exclude='.git' . $NEW_DIR/

# Test new environment
cd $NEW_DIR
source venv/bin/activate
python -m pytest tests/ -v

# Switch traffic
sudo rm -f $CURRENT_LINK
sudo ln -s $NEW_DIR $CURRENT_LINK

# Restart services
sudo systemctl restart options-trading.service

echo "Deployment to $NEW_ENV completed!"
```

## 📋 Maintenance

### Daily Tasks
- Check system health
- Verify data ingestion
- Monitor performance metrics
- Review error logs

### Weekly Tasks
- Update dependencies
- Run full system backup
- Performance optimization
- Security updates

### Monthly Tasks
- System maintenance
- Capacity planning
- Performance review
- Strategy optimization

---

**🔧 For troubleshooting and support:**
- Check logs: `journalctl -u options-trading.service -f`
- Monitor resources: `htop`, `nvidia-smi`
- Health check: `python scripts/health_check.py`
- Performance: `python scripts/performance_monitor.py`
