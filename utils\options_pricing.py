#!/usr/bin/env python3
"""
Options Pricing Utilities - Black-Scholes and Greeks Calculations

Features:
📊 1. Options Pricing Models
- Black-Scholes pricing
- Binomial tree pricing
- Monte Carlo pricing
- American options pricing

📈 2. Greeks Calculations
- Delta (price sensitivity)
- Gamma (delta sensitivity)
- Theta (time decay)
- Vega (volatility sensitivity)
- Rho (interest rate sensitivity)

⚡ 3. Implied Volatility
- Newton-Raphson method
- Bisection method
- <PERSON>'s method
- Volatility smile modeling

🎯 4. Advanced Features
- Dividend adjustments
- Early exercise premium
- Volatility surface construction
- Risk-neutral probability
"""

import numpy as np
import polars as pl
from scipy.stats import norm
from scipy.optimize import brentq, minimize_scalar
from typing import Union, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class OptionsBlackScholes:
    """Black-Scholes options pricing model"""
    
    @staticmethod
    def call_price(S: float, K: float, T: float, r: float, sigma: float, q: float = 0.0) -> float:
        """
        Calculate Black-Scholes call option price
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            r: Risk-free rate
            sigma: Volatility
            q: Dividend yield
            
        Returns:
            Call option price
        """
        try:
            if T <= 0:
                return max(S - K, 0)
            
            d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            call_price = (S * np.exp(-q * T) * norm.cdf(d1) - 
                         K * np.exp(-r * T) * norm.cdf(d2))
            
            return max(call_price, 0)
            
        except Exception as e:
            logger.error(f"Error calculating call price: {e}")
            return 0.0
    
    @staticmethod
    def put_price(S: float, K: float, T: float, r: float, sigma: float, q: float = 0.0) -> float:
        """
        Calculate Black-Scholes put option price
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            r: Risk-free rate
            sigma: Volatility
            q: Dividend yield
            
        Returns:
            Put option price
        """
        try:
            if T <= 0:
                return max(K - S, 0)
            
            d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            put_price = (K * np.exp(-r * T) * norm.cdf(-d2) - 
                        S * np.exp(-q * T) * norm.cdf(-d1))
            
            return max(put_price, 0)
            
        except Exception as e:
            logger.error(f"Error calculating put price: {e}")
            return 0.0

class OptionsGreeks:
    """Options Greeks calculations"""
    
    @staticmethod
    def delta(S: float, K: float, T: float, r: float, sigma: float, 
              option_type: str = 'call', q: float = 0.0) -> float:
        """Calculate option delta"""
        try:
            if T <= 0:
                if option_type.lower() == 'call':
                    return 1.0 if S > K else 0.0
                else:
                    return -1.0 if S < K else 0.0
            
            d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            
            if option_type.lower() == 'call':
                return np.exp(-q * T) * norm.cdf(d1)
            else:
                return -np.exp(-q * T) * norm.cdf(-d1)
                
        except Exception as e:
            logger.error(f"Error calculating delta: {e}")
            return 0.0
    
    @staticmethod
    def gamma(S: float, K: float, T: float, r: float, sigma: float, q: float = 0.0) -> float:
        """Calculate option gamma"""
        try:
            if T <= 0:
                return 0.0
            
            d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            
            gamma = (np.exp(-q * T) * norm.pdf(d1)) / (S * sigma * np.sqrt(T))
            
            return gamma
            
        except Exception as e:
            logger.error(f"Error calculating gamma: {e}")
            return 0.0
    
    @staticmethod
    def theta(S: float, K: float, T: float, r: float, sigma: float, 
              option_type: str = 'call', q: float = 0.0) -> float:
        """Calculate option theta (time decay)"""
        try:
            if T <= 0:
                return 0.0
            
            d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            if option_type.lower() == 'call':
                theta = ((-S * np.exp(-q * T) * norm.pdf(d1) * sigma) / (2 * np.sqrt(T)) -
                        r * K * np.exp(-r * T) * norm.cdf(d2) +
                        q * S * np.exp(-q * T) * norm.cdf(d1))
            else:
                theta = ((-S * np.exp(-q * T) * norm.pdf(d1) * sigma) / (2 * np.sqrt(T)) +
                        r * K * np.exp(-r * T) * norm.cdf(-d2) -
                        q * S * np.exp(-q * T) * norm.cdf(-d1))
            
            return theta / 365  # Convert to daily theta
            
        except Exception as e:
            logger.error(f"Error calculating theta: {e}")
            return 0.0
    
    @staticmethod
    def vega(S: float, K: float, T: float, r: float, sigma: float, q: float = 0.0) -> float:
        """Calculate option vega"""
        try:
            if T <= 0:
                return 0.0
            
            d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            
            vega = S * np.exp(-q * T) * norm.pdf(d1) * np.sqrt(T)
            
            return vega / 100  # Convert to percentage point change
            
        except Exception as e:
            logger.error(f"Error calculating vega: {e}")
            return 0.0
    
    @staticmethod
    def rho(S: float, K: float, T: float, r: float, sigma: float, 
            option_type: str = 'call', q: float = 0.0) -> float:
        """Calculate option rho"""
        try:
            if T <= 0:
                return 0.0
            
            d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            if option_type.lower() == 'call':
                rho = K * T * np.exp(-r * T) * norm.cdf(d2)
            else:
                rho = -K * T * np.exp(-r * T) * norm.cdf(-d2)
            
            return rho / 100  # Convert to percentage point change
            
        except Exception as e:
            logger.error(f"Error calculating rho: {e}")
            return 0.0

class ImpliedVolatility:
    """Implied volatility calculations"""
    
    @staticmethod
    def calculate_iv(market_price: float, S: float, K: float, T: float, r: float,
                    option_type: str = 'call', q: float = 0.0) -> float:
        """
        Calculate implied volatility using Brent's method
        
        Args:
            market_price: Market price of the option
            S: Current stock price
            K: Strike price
            T: Time to expiration
            r: Risk-free rate
            option_type: 'call' or 'put'
            q: Dividend yield
            
        Returns:
            Implied volatility
        """
        try:
            if T <= 0:
                return 0.0
            
            # Intrinsic value check
            if option_type.lower() == 'call':
                intrinsic = max(S - K, 0)
            else:
                intrinsic = max(K - S, 0)
            
            if market_price <= intrinsic:
                return 0.0
            
            def objective(sigma):
                if option_type.lower() == 'call':
                    theoretical_price = OptionsBlackScholes.call_price(S, K, T, r, sigma, q)
                else:
                    theoretical_price = OptionsBlackScholes.put_price(S, K, T, r, sigma, q)
                
                return theoretical_price - market_price
            
            # Use Brent's method to find implied volatility
            try:
                iv = brentq(objective, 0.001, 5.0, xtol=1e-6, maxiter=100)
                return max(iv, 0.001)
            except ValueError:
                # If Brent's method fails, try a simple search
                return ImpliedVolatility._simple_iv_search(market_price, S, K, T, r, option_type, q)
                
        except Exception as e:
            logger.error(f"Error calculating implied volatility: {e}")
            return 0.20  # Default volatility
    
    @staticmethod
    def _simple_iv_search(market_price: float, S: float, K: float, T: float, r: float,
                         option_type: str, q: float) -> float:
        """Simple grid search for implied volatility"""
        try:
            best_iv = 0.20
            best_diff = float('inf')
            
            for iv in np.arange(0.01, 2.0, 0.01):
                if option_type.lower() == 'call':
                    theoretical_price = OptionsBlackScholes.call_price(S, K, T, r, iv, q)
                else:
                    theoretical_price = OptionsBlackScholes.put_price(S, K, T, r, iv, q)
                
                diff = abs(theoretical_price - market_price)
                
                if diff < best_diff:
                    best_diff = diff
                    best_iv = iv
            
            return best_iv
            
        except Exception as e:
            logger.error(f"Error in simple IV search: {e}")
            return 0.20

class VolatilitySurface:
    """Volatility surface construction and analysis"""
    
    @staticmethod
    def construct_surface(option_data: pl.DataFrame, spot_price: float, 
                         risk_free_rate: float) -> pl.DataFrame:
        """
        Construct volatility surface from option data
        
        Args:
            option_data: DataFrame with option prices and parameters
            spot_price: Current underlying price
            risk_free_rate: Risk-free rate
            
        Returns:
            DataFrame with volatility surface
        """
        try:
            surface_data = []
            
            for row in option_data.iter_rows(named=True):
                # Calculate time to expiration
                expiry_date = row['expiry_date']
                # Simplified: assume 30 days to expiry
                T = 30 / 365.0
                
                # Calculate implied volatility
                iv = ImpliedVolatility.calculate_iv(
                    market_price=row['market_price'],
                    S=spot_price,
                    K=row['strike_price'],
                    T=T,
                    r=risk_free_rate,
                    option_type=row['option_type']
                )
                
                # Calculate moneyness
                moneyness = row['strike_price'] / spot_price
                
                surface_data.append({
                    'strike_price': row['strike_price'],
                    'expiry_date': expiry_date,
                    'time_to_expiry': T,
                    'moneyness': moneyness,
                    'implied_volatility': iv,
                    'option_type': row['option_type']
                })
            
            return pl.DataFrame(surface_data)
            
        except Exception as e:
            logger.error(f"Error constructing volatility surface: {e}")
            return pl.DataFrame()

# Utility functions for vectorized calculations
def vectorized_black_scholes(S: np.ndarray, K: np.ndarray, T: np.ndarray, 
                           r: float, sigma: np.ndarray, option_type: str) -> np.ndarray:
    """Vectorized Black-Scholes calculation for multiple options"""
    try:
        # Handle zero time to expiry
        T = np.maximum(T, 1e-8)
        
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        if option_type.lower() == 'call':
            prices = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
        else:
            prices = K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
        
        return np.maximum(prices, 0)
        
    except Exception as e:
        logger.error(f"Error in vectorized Black-Scholes: {e}")
        return np.zeros_like(S)

def vectorized_greeks(S: np.ndarray, K: np.ndarray, T: np.ndarray, 
                     r: float, sigma: np.ndarray, option_type: str) -> dict:
    """Vectorized Greeks calculation for multiple options"""
    try:
        # Handle zero time to expiry
        T = np.maximum(T, 1e-8)
        
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        # Delta
        if option_type.lower() == 'call':
            delta = norm.cdf(d1)
        else:
            delta = -norm.cdf(-d1)
        
        # Gamma
        gamma = norm.pdf(d1) / (S * sigma * np.sqrt(T))
        
        # Theta
        if option_type.lower() == 'call':
            theta = ((-S * norm.pdf(d1) * sigma) / (2 * np.sqrt(T)) -
                    r * K * np.exp(-r * T) * norm.cdf(d2))
        else:
            theta = ((-S * norm.pdf(d1) * sigma) / (2 * np.sqrt(T)) +
                    r * K * np.exp(-r * T) * norm.cdf(-d2))
        
        theta = theta / 365  # Convert to daily
        
        # Vega
        vega = S * norm.pdf(d1) * np.sqrt(T) / 100
        
        # Rho
        if option_type.lower() == 'call':
            rho = K * T * np.exp(-r * T) * norm.cdf(d2) / 100
        else:
            rho = -K * T * np.exp(-r * T) * norm.cdf(-d2) / 100
        
        return {
            'delta': delta,
            'gamma': gamma,
            'theta': theta,
            'vega': vega,
            'rho': rho
        }
        
    except Exception as e:
        logger.error(f"Error in vectorized Greeks: {e}")
        return {
            'delta': np.zeros_like(S),
            'gamma': np.zeros_like(S),
            'theta': np.zeros_like(S),
            'vega': np.zeros_like(S),
            'rho': np.zeros_like(S)
        }
