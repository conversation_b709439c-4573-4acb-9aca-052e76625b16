#!/usr/bin/env python3
"""
Pytest Configuration and Fixtures for Options Trading System Tests

Features:
🧪 1. Test Configuration
- Pytest configuration
- Test markers setup
- Test data fixtures
- Mock configurations

📊 2. Data Fixtures
- Sample options data
- Mock market data
- Test configurations
- Temporary directories

⚡ 3. Performance Testing
- Benchmark fixtures
- Memory monitoring
- Speed testing utilities
- Load testing setup

🎯 4. Integration Testing
- Agent mocking
- API mocking
- Database mocking
- External service mocking
"""

import pytest
import asyncio
import polars as pl
import numpy as np
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
import logging

# Configure logging for tests
logging.basicConfig(level=logging.INFO)

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def temp_data_dir():
    """Create temporary data directory for testing"""
    temp_dir = tempfile.mkdtemp(prefix="options_test_")
    temp_path = Path(temp_dir)
    
    # Create subdirectories
    subdirs = [
        "historical/1min", "historical/3min", "historical/5min", "historical/15min",
        "live/1min", "live/3min", "live/5min", "live/15min",
        "features/1min", "features/3min", "features/5min", "features/15min",
        "strategies", "backtest", "models", "greeks", "volatility"
    ]
    
    for subdir in subdirs:
        (temp_path / subdir).mkdir(parents=True, exist_ok=True)
    
    yield temp_path
    shutil.rmtree(temp_dir)

@pytest.fixture
def sample_nifty_options_data():
    """Generate sample NIFTY options data for testing"""
    data = []
    base_time = datetime(2024, 1, 15, 9, 15)  # Market start time
    
    # Generate 1 hour of 1-minute data
    for i in range(60):
        timestamp = base_time + timedelta(minutes=i)
        
        # NIFTY strikes around 25000
        for strike in [24500, 24750, 25000, 25250, 25500]:
            for option_type in ['CE', 'PE']:
                # Generate realistic option prices
                spot_price = 25000 + np.random.normal(0, 50)
                moneyness = spot_price / strike
                
                if option_type == 'CE':
                    intrinsic = max(spot_price - strike, 0)
                    time_value = 50 + np.random.normal(0, 10)
                else:
                    intrinsic = max(strike - spot_price, 0)
                    time_value = 50 + np.random.normal(0, 10)
                
                option_price = intrinsic + time_value
                
                data.append({
                    'timestamp': timestamp,
                    'symbol': f"NIFTY{strike}{option_type}",
                    'underlying': 'NIFTY',
                    'strike_price': strike,
                    'expiry_date': '2024-01-25',
                    'option_type': option_type,
                    'open': option_price + np.random.normal(0, 2),
                    'high': option_price + abs(np.random.normal(0, 3)),
                    'low': option_price - abs(np.random.normal(0, 3)),
                    'close': option_price + np.random.normal(0, 2),
                    'volume': np.random.randint(100, 1000),
                    'open_interest': np.random.randint(5000, 50000)
                })
    
    return pl.DataFrame(data)

@pytest.fixture
def sample_banknifty_options_data():
    """Generate sample BANK NIFTY options data for testing"""
    data = []
    base_time = datetime(2024, 1, 15, 9, 15)
    
    # Generate 1 hour of 1-minute data
    for i in range(60):
        timestamp = base_time + timedelta(minutes=i)
        
        # BANK NIFTY strikes around 48000
        for strike in [47500, 47750, 48000, 48250, 48500]:
            for option_type in ['CE', 'PE']:
                # Generate realistic option prices
                spot_price = 48000 + np.random.normal(0, 100)
                
                if option_type == 'CE':
                    intrinsic = max(spot_price - strike, 0)
                    time_value = 100 + np.random.normal(0, 20)
                else:
                    intrinsic = max(strike - spot_price, 0)
                    time_value = 100 + np.random.normal(0, 20)
                
                option_price = intrinsic + time_value
                
                data.append({
                    'timestamp': timestamp,
                    'symbol': f"BANKNIFTY{strike}{option_type}",
                    'underlying': 'BANKNIFTY',
                    'strike_price': strike,
                    'expiry_date': '2024-01-25',
                    'option_type': option_type,
                    'open': option_price + np.random.normal(0, 5),
                    'high': option_price + abs(np.random.normal(0, 8)),
                    'low': option_price - abs(np.random.normal(0, 8)),
                    'close': option_price + np.random.normal(0, 5),
                    'volume': np.random.randint(50, 500),
                    'open_interest': np.random.randint(2000, 20000)
                })
    
    return pl.DataFrame(data)

@pytest.fixture
def sample_greeks_data():
    """Generate sample Greeks data for testing"""
    data = []
    base_time = datetime(2024, 1, 15, 9, 15)
    
    for i in range(10):
        timestamp = base_time + timedelta(minutes=i * 5)
        
        for strike in [24500, 25000, 25500]:
            for option_type in ['CE', 'PE']:
                # Generate realistic Greeks
                if option_type == 'CE':
                    delta = 0.3 + np.random.normal(0, 0.1)
                    gamma = 0.001 + abs(np.random.normal(0, 0.0005))
                    theta = -2.0 + np.random.normal(0, 0.5)
                    vega = 15.0 + np.random.normal(0, 3)
                else:
                    delta = -0.3 + np.random.normal(0, 0.1)
                    gamma = 0.001 + abs(np.random.normal(0, 0.0005))
                    theta = -2.0 + np.random.normal(0, 0.5)
                    vega = 15.0 + np.random.normal(0, 3)
                
                data.append({
                    'timestamp': timestamp,
                    'symbol': f"NIFTY{strike}{option_type}",
                    'underlying': 'NIFTY',
                    'strike_price': strike,
                    'option_type': option_type,
                    'delta': delta,
                    'gamma': gamma,
                    'theta': theta,
                    'vega': vega,
                    'rho': 0.1 + np.random.normal(0, 0.02),
                    'implied_vol': 0.15 + abs(np.random.normal(0, 0.03))
                })
    
    return pl.DataFrame(data)

@pytest.fixture
def sample_strategy():
    """Generate sample options strategy for testing"""
    return {
        'strategy_id': 'test_long_call_001',
        'strategy_type': 'long_call',
        'underlying': 'NIFTY',
        'description': 'Long Call strategy for testing',
        'legs': [
            {
                'symbol': 'NIFTY25000CE',
                'option_type': 'CE',
                'strike_price': 25000,
                'expiry_date': '2024-01-25',
                'quantity': 1,
                'action': 'BUY',
                'premium': 100.0
            }
        ],
        'max_loss': 100.0,
        'max_profit': float('inf'),
        'breakeven_points': [25100.0],
        'margin_required': 5000.0,
        'risk_reward_ratio': float('inf')
    }

@pytest.fixture
def mock_smartapi():
    """Mock SmartAPI for testing"""
    mock_api = Mock()
    mock_api.generateSession.return_value = {'status': True, 'data': {'jwtToken': 'test_token'}}
    mock_api.getProfile.return_value = {'status': True, 'data': {'clientcode': 'TEST123'}}
    mock_api.ltpData.return_value = {
        'status': True,
        'data': {
            'NIFTY25000CE': {'ltp': 100.0, 'close': 95.0},
            'NIFTY25000PE': {'ltp': 80.0, 'close': 85.0}
        }
    }
    return mock_api

@pytest.fixture
def mock_config():
    """Mock configuration for testing"""
    return {
        'underlying_symbols': ['NIFTY', 'BANKNIFTY'],
        'timeframes': ['1min', '3min', '5min', '15min'],
        'risk_free_rate': 0.06,
        'max_positions': 5,
        'risk_per_trade': 0.02,
        'transaction_cost': 20.0,
        'slippage': 0.01
    }

@pytest.fixture
def performance_monitor():
    """Performance monitoring fixture"""
    import psutil
    import time
    
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.start_memory = None
            self.process = psutil.Process()
        
        def start(self):
            self.start_time = time.time()
            self.start_memory = self.process.memory_info().rss
        
        def stop(self):
            end_time = time.time()
            end_memory = self.process.memory_info().rss
            
            return {
                'execution_time': end_time - self.start_time,
                'memory_used': (end_memory - self.start_memory) / 1024 / 1024,  # MB
                'peak_memory': self.process.memory_info().peak_wss / 1024 / 1024 if hasattr(self.process.memory_info(), 'peak_wss') else 0
            }
    
    return PerformanceMonitor()

# Test markers
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "unit: Unit tests"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests"
    )
    config.addinivalue_line(
        "markers", "performance: Performance tests"
    )
    config.addinivalue_line(
        "markers", "slow: Slow running tests"
    )
    config.addinivalue_line(
        "markers", "agent: Agent-specific tests"
    )

# Test collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify test collection"""
    for item in items:
        # Add markers based on test names
        if "performance" in item.name or "benchmark" in item.name:
            item.add_marker(pytest.mark.performance)
        
        if "integration" in item.name or "end_to_end" in item.name:
            item.add_marker(pytest.mark.integration)
        
        if "agent" in item.name:
            item.add_marker(pytest.mark.agent)

# Async test utilities
@pytest.fixture
def async_test_timeout():
    """Default timeout for async tests"""
    return 30  # 30 seconds

# Database mocking
@pytest.fixture
def mock_database():
    """Mock database for testing"""
    class MockDatabase:
        def __init__(self):
            self.data = {}
        
        async def save(self, key, value):
            self.data[key] = value
        
        async def load(self, key):
            return self.data.get(key)
        
        async def delete(self, key):
            if key in self.data:
                del self.data[key]
        
        async def list_keys(self):
            return list(self.data.keys())
    
    return MockDatabase()

# Logging configuration for tests
@pytest.fixture(autouse=True)
def configure_test_logging():
    """Configure logging for tests"""
    logging.getLogger().setLevel(logging.WARNING)  # Reduce noise during tests
    yield
    logging.getLogger().setLevel(logging.INFO)  # Reset after tests
